import { IAppConfig } from "../../../models/response/common/IAppConfig";
import { eAppConfigActionTypeIds } from "../../action-types/common/IAppConfigActionType";
import { baseAction } from "../IActionBase";

export const appConfigAction={
    loadStart:()=> baseAction(eAppConfigActionTypeIds.LOAD_START,undefined),
    loadSuccess:(payload:IAppConfig)=> baseAction(eAppConfigActionTypeIds.LOAD_SUCCESS,payload),
    loadError:(message?:string)=> baseAction(eAppConfigActionTypeIds.LOAD_ERROR,message),
}