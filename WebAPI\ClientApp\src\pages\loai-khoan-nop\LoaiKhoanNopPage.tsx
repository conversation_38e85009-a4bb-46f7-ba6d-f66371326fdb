import {
    PlusIcon,
    SyncIcon,
    TrashIcon
} from "@primer/octicons-react";
import { Box } from '@primer/react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { Transition } from 'semantic-ui-react';
import { AnimationConfirm } from '../../components/confirm';
import Button from '../../components/ui/button';
import DataTable from "../../components/ui/data-table";
import Text from "../../components/ui/text";
import { PageBaseStatus } from '../../models/pageBaseStatus';
import { sf_loaikhoannop } from '../../models/response/category/sf_loaikhoannop';
import { changeLoaiKhoanNopSelectionIds, closeLoaiKhoanNopDetailModal, deleteLoaiKhoanNopStart, loadLoaiKhoanNopStart, saveLoaiKhoanNopStart, showLoaiKhoanNopDeleteConfirm, showLoaiKhoanNopDetailModal } from '../../state/actions/loaiKhoanNopActions';
import { RootState } from '../../state/reducers';
import LoaiKhoanNopDetailModal from './detail_modal';
import LoaiKhoanNopEditModal from "./LoaiKhoanNopEditModal";

const LoaiKhoanNopPage = () => {
    const [danhSachLoaiKhoanNopSelectedIds, setDanhSachLoaiKhoanNopSelectedIds] = useState<number[]>([]);

    const state = useSelector((state: RootState) => state.loaiKhoanNop)
    const { dm_coso_id } = useSelector((state: RootState) => state.common)
    const dispatch = useDispatch();
    const grid = useRef<any>();
    const {
        status,
        sf_loaikhoannops,
        sf_loaikhoannop_selected_ids,
        sf_loaikhoannop_editing,
        is_show_detail_modal,
        is_show_delete_confirm
    } = state;

    const dataSource = useMemo(() => {
        return sf_loaikhoannops.filter(x => x.dm_coso_id === dm_coso_id)
    }, [sf_loaikhoannops, dm_coso_id])
    useEffect(() => {
        dispatch(loadLoaiKhoanNopStart())
    }, [])


    const handleRefresh = useCallback(() => {
        dispatch(loadLoaiKhoanNopStart());
        onSelectionChanged([])
        setDanhSachLoaiKhoanNopSelectedIds([])
    }, [])

    const handleCloseDeleteConfirm = useCallback(() => {
        dispatch(showLoaiKhoanNopDeleteConfirm(false))
    }, [])

    const handleShowDeleteConfirm = useCallback(() => {
        dispatch(showLoaiKhoanNopDeleteConfirm(true))
    }, [])

    const handleBtnInsertClick = useCallback(() => {
        dispatch(showLoaiKhoanNopDetailModal());
    }, []);

    const handleBtnEditClicked = (data: sf_loaikhoannop) => {
        dispatch(showLoaiKhoanNopDetailModal(data));
    }
    const handleExport = useCallback(() => {
        if (grid && grid.current && grid.current.instance) {
            grid.current.instance.exportToExcel();
        }
    }, [grid])
    const onSelectionChanged = ({ selectedRowKeys }: any) => {
        dispatch(changeLoaiKhoanNopSelectionIds(selectedRowKeys))

    };
    const handleConfirmDelete = useCallback(() => {
        dispatch(deleteLoaiKhoanNopStart(sf_loaikhoannop_selected_ids))
    }, [sf_loaikhoannop_selected_ids]);

    const handleCloseDetailModal = useCallback(() => {
        dispatch(closeLoaiKhoanNopDetailModal())
    }, [])
    const handleSubmitDetailModal = useCallback((data: sf_loaikhoannop) => {
        dispatch(saveLoaiKhoanNopStart(data))
        dispatch(closeLoaiKhoanNopDetailModal())
    }, [])
    return (
        <React.Fragment>
            <Box sx={{ p: 2, pt: 0 }}>
                <Transition animation="scale"
                    visible={true}
                    duration={100}
                    transitionOnMount={true}>
                    <Box>
                        <Box sx={{ m: 2 }}>
                            <DataTable
                                height="calc(100vh - 240px)"
                                titleComponent={<Text text="Danh sách loại khoản nộp" sx={{ fontSize: 16, fontWeight: 600 }} />}
                                data={dataSource}
                                emptyComponent={<Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        minHeight: "60vh",
                                        textAlign: "center"
                                    }}
                                >
                                    <img
                                        src="https://true-north-school.s3.cloudfly.vn/fee/datagrid_empty"
                                        alt="Không có dữ liệu"
                                        style={{ width: "60px", height: "60px", marginBottom: "5px" }}
                                    />
                                    <Box sx={{ fontSize: "13px", fontWeight: "400", color: "#8C8C8C" }}>
                                        Chưa có dữ liệu loại khoản nộp
                                    </Box>
                                </Box>
                                }
                                selection={{
                                    mode: "multiple",
                                    keyExpr: "id",
                                    selectedRowKeys: danhSachLoaiKhoanNopSelectedIds,
                                    onSelectionChanged(keys) {
                                        setDanhSachLoaiKhoanNopSelectedIds(keys)
                                    },
                                }}
                                exportEnable
                                filterRow={{
                                    enable: true
                                }}
                                actionComponent={
                                    <Box sx={{
                                        display: "flex"
                                    }}>
                                        <Button sx={{ ml: 2 }} variant="default" size="small" leadingVisual={SyncIcon} onClick={handleRefresh}>Làm mới</Button>
                                        <Button sx={{ ml: 2 }} variant="primary" size="small" leadingVisual={PlusIcon} onClick={handleBtnInsertClick}>Thêm mới</Button>
                                        {/* <Button sx={{ ml: 2 }} leadingVisual={DownloadIcon} variant="default" size="medium" onClick={handleExport}>Xuất excel</Button> */}
                                        {danhSachLoaiKhoanNopSelectedIds && danhSachLoaiKhoanNopSelectedIds.length > 0 &&
                                            <Button sx={{ ml: 2 }} leadingVisual={TrashIcon} variant="danger" size="small" onClick={handleShowDeleteConfirm}>Xóa {danhSachLoaiKhoanNopSelectedIds.length} loại khoản nộp</Button>
                                        }
                                    </Box>
                                }
                                columns={[
                                    {
                                        caption: "Sửa",
                                        id: "edit",
                                        width: "50px",
                                        align: "center",
                                        isAllowFocus: false,
                                        cellRender: useCallback((data: any) => (
                                            <a id={`btnEdit${data.id}`} style={{ cursor: "pointer", color: "#3c22ff" }}>
                                                <i onClick={() => handleBtnEditClicked(data)} className="cmd-edit-icon fas fa-edit"></i>
                                            </a>
                                        ), [handleBtnEditClicked]),
                                    },
                                    {
                                        caption: "Loại khoản nộp (vi)",
                                        id: "loai_khoan_nop",
                                        dataField: "loai_khoan_nop",
                                        isAllowFocus: false,
                                    },
                                    {
                                        caption: "Loại khoản nộp (en)",
                                        id: "loai_khoan_nop_en",
                                        dataField: "loai_khoan_nop_en",
                                        isAllowFocus: false,
                                    },
                                ]} paging={{
                                    enable: true,
                                    pageSizeItems: [50, 100, 200, 500]
                                }}
                            />
                            {is_show_detail_modal && <LoaiKhoanNopEditModal
                                title={sf_loaikhoannop_editing ? `Cập nhật` : "Thêm mới"}
                                sf_loaikhoannop={sf_loaikhoannop_editing}
                                onClose={handleCloseDetailModal}
                                onSubmit={handleSubmitDetailModal}
                                is_saving={status === PageBaseStatus.is_saving}
                                animationOf={sf_loaikhoannop_editing ? `#btnEdit${sf_loaikhoannop_editing.id}` : "#btnInsert"}
                            />
                            }
                        </Box>

                    </Box>
                </Transition>
                {is_show_delete_confirm &&
                    <AnimationConfirm
                        onClose={handleCloseDeleteConfirm}
                        onConfirmed={handleConfirmDelete}
                        text={`Bạn có chắc chắn muốn xóa ${danhSachLoaiKhoanNopSelectedIds.length} dòng đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_deleting}
                        text_close_button="Không xóa"
                        text_confirm_button="Xóa"
                        type='danger'
                        animationOf='#btnDelete'

                    />
                }
            </Box>
        </React.Fragment>
    );
}
export default LoaiKhoanNopPage;