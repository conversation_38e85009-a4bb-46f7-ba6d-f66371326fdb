
import { ActionList, ActionMenu, Select } from '@primer/react';
import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { dm_he } from '../../models/response/category/dm_he';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
type ComboboxHeProps = {
    isReadonly?: boolean;
    value?: number;
    onValueChanged: (id: number, data?: dm_he) => void;
    className?: string;
    isShowClearButton?: boolean;
    preText?: string;
    width?: string | number;
    dm_khoi_id?: number;
    dm_truong_id?: number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    size?: "small" | "medium" | "large" 
};
export const ComboboxHe = (props: ComboboxHeProps) => {
    const { translate } = useCommonContext();
    const dispatch = useDispatch();
    const categorySource = useSelector((state: RootState) => state.categorySource);

    useEffect(() => {
        if (categorySource.dm_hes.length === 0) dispatch(actions.categorySource.loadHeStart());
    }, []);

    const source: dm_he[] = useMemo(() => {
        return categorySource.dm_hes.map(x => ({ ...x }));
    }, [categorySource.dm_hes, props.dm_truong_id]);
    const selectedData = useMemo(() => {
        if (props.value && source) {
            return source.find(x => x.id == props.value)
        }
        return undefined
    }, [source, props.value])
    return (
        <div style={{ width: '100%' }}>
            <ActionMenu>
                <ActionMenu.Button
                    aria-label="Select school year"
                    size={props.size}
                    style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between'
                    }}
                >
                    {selectedData ? selectedData.ten_he : "Chọn hệ"}
                </ActionMenu.Button>
                <ActionMenu.Overlay width="small">
                    <ActionList selectionVariant="single">
                        {props.isShowClearButton &&
                            <ActionList.Item key={0} selected={props.value != undefined && 0 === props.value}
                                onSelect={() => {
                                    props.onValueChanged(0)
                                }}
                            >
                                {"Chọn hệ"}
                            </ActionList.Item>
                        }
                        {source && source.map((item, index) => {
                            return (
                                <ActionList.Item key={item.id} selected={props.value != undefined && item.id === props.value}
                                    onSelect={() => {
                                        props.onValueChanged(item.id)
                                    }}
                                >
                                    {item.ten_he}
                                </ActionList.Item>
                            );
                        })}
                    </ActionList>
                </ActionMenu.Overlay>
            </ActionMenu>
        </div>
    );
}

