import {
    ArrowUpRightIcon,
    BellIcon,
    CheckIcon,
    DownloadIcon,
    FileIcon,
    LinkIcon,
    PlusIcon,
    ProjectRoadmapIcon,
    SyncIcon,
    TrashIcon,
    XIcon
} from "@primer/octicons-react";
import { ActionList, ActionMenu, Box, Checkbox } from '@primer/react';
import * as XLSX from 'xlsx';

import Toolbar, { Item } from 'devextreme-react/toolbar';
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { Transition } from 'semantic-ui-react';
import { SF_TBTP_DELETE_API, SF_TBTP_INSERT_API, tbtpApi } from '../../api/tbtpApi';
import { ComboboxHe } from '../../components/combobox-he';
import ComboboxKhoi from '../../components/combobox-khoi';
import { ComboboxTruong } from '../../components/combobox-truong';
import { AnimationConfirm } from '../../components/confirm';
import Text from "../../components/ui/text";
import { useCommonContext } from '../../contexts/common';
import { localStorageHelper } from '../../helpers/localStorage';
import { PageBaseStatus } from '../../models/pageBaseStatus';
import { sf_tbtp_viewmodel } from '../../models/response/tbtp/sf_tbtp';
import {
    saveDotGuiStart
} from '../../state/actions/dotGuiActions';
import {
    autoHachToanStart,
    changeTBTPHe, changeTBTPKhoi,
    changeTBTPNamHoc, changeTBTPTruong,
    changeTBTPeSelectionIds,
    closeAutoHachToanConfirm, closeEditModal,
    closePaymentConfirm,
    closePdfConfirm, closeTBTPDeleteConfirm,
    closeTBTPDetailModal, closeTBTPDotGuiDetailModal, closeTBTPSendConfirm,
    createPaymentStart,
    createPdfStart, deleteTBTPStart, loadTBTPStart,
    sendTBTPStart,
    showAutoHachToanConfirm,
    showEditModal,
    showPaymentConfirm,
    showPdfConfirm,
    showTBTPDeleteConfirm, showTBTPDetailModal, showTBTPDotGuiDetailModal, showTBTPSendConfirm
} from '../../state/actions/tbtpActions';

import { DateBox, NumberBox, TextBox } from 'devextreme-react';
import moment from 'moment';
import { hinhThucNopBomApi } from '../../api/hinhThucNopBom';
import CmbDotGuiTBTP from '../../components/combobox-dotgui-tbtp';
import CmbHinhThucNopBom from '../../components/combobox-hinh-thuc-nop-bom';
import { DefaultPopup } from '../../components/modal';
import SelectBoxTrangThaiHocSinhMultiple from "../../components/selectbox-trangthai-hocsinh-multiple";
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import { showNotify } from '../../helpers/toast';
import { sf_tbtp_delete_rq } from '../../models/request/tbtp/sf_tbtp_delete_rq';
import { sf_tbtp_select_rq_dotgui } from "../../models/request/tbtp/sf_tbtp_select_rq";
import { sf_tbtp_send_remind_rq } from '../../models/request/tbtp/sf_tbtp_send_remind_rq';
import { sf_hinhthucnopbom } from '../../models/response/category/sf_hinhthucnopbom';
import { sf_dotgui_tbtp } from '../../models/response/dot-gui/sf_dotgui_tbtp';
import { loadKhoanNopStart } from '../../state/actions/khoanNopActions';
import { RootState } from '../../state/reducers';
import AlphaReportModal from './alpha-report';
import TBTPCreateModal from './create-modal';
import EditGhiChuTBTPModal from './edit-ghichu-tbtp-modal/EditGhiChuTBTPModal';
import { TBTPEditModal } from './edit_tbtp_modal';
import TBTPStatusSummary from "./status-summary";
const TBTPPage = () => {
    const { createUUID, checkAccesiableTo, getDataGridStorageKey } = useCommonContext();
    const { nam_hoc: param_nam_hoc, dm_coso_id } = useSelector((x: RootState) => x.common)
    const { dm_truongs_treeview } = useSelector((x: RootState) => x.categorySource)
    const { dm_hes } = useSelector((x: RootState) => x.categorySource)
    const [selectedStatusStudent, setSelectedStatusStudent] = useState<number[]>([]);
    const { status: statusKhoanNops, sf_khoannops } = useSelector((x: RootState) => x.khoanNop)
    const [danhSachTBTPSelectedIds, setDanhSachTBTPSelectedIds] = useState<number[]>([]);

    const dispatch = useDispatch();
    const [hinhThucNopBomSelectedId, setHinhThucNopBomSelectedId] = useState<number>(0);
    const [sendFilterValue, setSendFilterValue] = useState<boolean>();
    const [isShowGhiChuModal, setIsShowGhiChuModal] = useState<boolean>(false);
    const [isShowDotGuiModal, setIsShowDotGuiModal] = useState<boolean>(false);
    const [dataSourceFilter, setDataSourceFilter] = useState<sf_tbtp_viewmodel[]>([]);

    const [hinhThucNopBoms, setHinhThucNopBoms] = useState<sf_hinhthucnopbom[]>([]);
    const [hinhThucNopBomId, setHinhThucNopBomId] = useState<number>(0);
    const [is_show_send_remind_confirm, set_is_show_send_remind_confirm] = useState<boolean>(false);
    const [han_nop_nhac_no, set_han_nop_nhac_no] = useState<any>(
        moment().format("YYYY-MM-DD HH:mm:ss")
    );
    const [noi_dung_nhac_no, set_noi_dung_nhac_no] = useState<string>('');
    const [lan_nhac_no, set_lan_nhac_no] = useState<number>(1);

    const [isLoading, set_IsLoading] = useState<boolean>(false);

    const state = useSelector((x: RootState) => x.tbtp);
    const [dotGuiId, setDotGuiId] = useState<number>(0);
    const [dotGui, setDotGui] = useState<sf_dotgui_tbtp>()

    const [isShowAlphaDetail, setIsShowAlphaDetail] = useState(false);
    const [is_show_print_pdf_confirm, set_is_show_print_pdf_confirm] = useState<boolean>(false);

    const { status,
        nam_hoc,
        dm_truong_id,
        dm_he_id,
        dm_khoi_id,
        sf_tbtp_selected_ids,
        sf_tbtp_viewmodels,
        is_show_detail_modal,
        is_show_delete_confirm,
        is_show_edit_modal,
        sf_tbtp_editing,
        is_show_pdf_confirm,
        is_show_auto_hachtoan_confirm,
        is_show_send_confirm,
        is_show_payment_confirm
    } = state;
    const dmTruongSelected: any = useMemo(() => {
        const x: any = dm_truongs_treeview.find(x => x.id === dm_truong_id)
        return x;
    }, [dm_truongs_treeview, dm_truong_id])
    const dmHeSelected: any = useMemo(() => {
        const x: any = dm_hes.find(x => x.id === dm_he_id)
        return x;
    }, [dm_hes, dm_he_id])
    useEffect(() => {
        dispatch(changeTBTPNamHoc(param_nam_hoc))
    }, [param_nam_hoc])
    useEffect(() => {
        if (sf_khoannops.length === 0) {
            dispatch(loadKhoanNopStart())
        }
    }, [statusKhoanNops])
    //
    useEffect(() => {
        if (dotGui && sf_tbtp_viewmodels) {
            setDataSourceFilter(sf_tbtp_viewmodels)
        }
        else {
            setDataSourceFilter([])

        }
    }, [sf_tbtp_viewmodels, dotGui])
    useEffect(() => {
        if (status === PageBaseStatus.is_need_reload)
            handleReload();
    }, [status])
    useEffect(() => {
        handleReload();
    }, [nam_hoc, dm_truong_id, dm_khoi_id, dm_he_id, dotGuiId])

    const handleReload = () => {
        dispatch(loadTBTPStart({
            nam_hoc: nam_hoc,
            dm_he_id: dm_he_id,
            dm_khoi_id: dm_khoi_id,
            dm_truong_id: dm_truong_id,
            sf_dotgui_tbtp_id: dotGuiId
        }));
        localStorageHelper.setNamHoc(nam_hoc)
        localStorageHelper.setDmHeId(dm_he_id)
        localStorageHelper.setDmKhoiId(dm_khoi_id)
        localStorageHelper.setDmTruongId(dm_truong_id)
        setDanhSachTBTPSelectedIds([])
    }


    useEffect(() => {
        onSelectionChanged([])
    }, [hinhThucNopBomSelectedId])
    useEffect(() => {
        handleReloadHinhThucNopBoms();
    }, [])
    const handleReloadHinhThucNopBoms = async () => {
        const res = await hinhThucNopBomApi.selectAll();
        if (res.is_success) {
            setHinhThucNopBoms(res.data.filter((x: any) => x.id > 0))
        }
    }
    const dataSource = useMemo(() => {
        let data = sf_tbtp_viewmodels.filter(x => x.sf_hinhthucnopbom_id === hinhThucNopBomSelectedId || hinhThucNopBomSelectedId === 0)
            .filter(x => sendFilterValue === undefined || x.is_sended === sendFilterValue);
        if (dotGuiId > 0) {
            data = data.filter(dg => dg.sf_dotgui_tbtp_id === dotGuiId);
        }
        if (selectedStatusStudent.length > 0) {
            data = data.filter(x => selectedStatusStudent.includes(x.dm_trangthaihocsinh_id));
        }
        return data;

    }, [hinhThucNopBomSelectedId, sf_tbtp_viewmodels, sendFilterValue, dotGuiId, selectedStatusStudent])

    const grid = useRef<any>();
    const dataGridStateStorageKey = getDataGridStorageKey("tbtp")
    const isAccessibleInsert = useMemo(() => {
        if (!checkAccesiableTo(SF_TBTP_INSERT_API, 'POST')) return false;
        if (Array.isArray(danhSachTBTPSelectedIds) && danhSachTBTPSelectedIds.length > 0) return false;
        return true;
    }, [checkAccesiableTo, danhSachTBTPSelectedIds]);


    const isAccessibleDelete = useMemo(() => {
        if (!checkAccesiableTo(SF_TBTP_DELETE_API, 'DELETE')) return false;
        if (Array.isArray(danhSachTBTPSelectedIds) && danhSachTBTPSelectedIds.length <= 0) return false;
        return true;
    }, [checkAccesiableTo, danhSachTBTPSelectedIds]);


    const isAccessiblePdf = useMemo(() => {
        if (!checkAccesiableTo(SF_TBTP_INSERT_API, 'POST')) return false;
        if (Array.isArray(danhSachTBTPSelectedIds) && danhSachTBTPSelectedIds.length <= 0) return false;
        return true;
    }, [checkAccesiableTo, danhSachTBTPSelectedIds]);



    const handleRefresh = () => {
        setDanhSachTBTPSelectedIds([])
        handleReload();
        onSelectionChanged([])
    }
    const handleExport = useCallback(() => {
        if (grid && grid.current && grid.current.instance) {
            grid.current.instance.exportToExcel();
        }
    }, [grid])

    const handleCloseDeleteConfirm = useCallback(() => {
        dispatch(closeTBTPDeleteConfirm())
    }, [])
    const handleShowDeleteConfirm = useCallback(() => {
        dispatch(showTBTPDeleteConfirm())
    }, [])

    const handleShowPdfConfirm = useCallback(() => {
        dispatch(showPdfConfirm(danhSachTBTPSelectedIds))
    }, [])
    const handleShowPaymentConfirm = useCallback(() => {
        dispatch(showPaymentConfirm(danhSachTBTPSelectedIds))
    }, [])
    const handleShowAutoHachToanConfirm = useCallback(() => {
        dispatch(showAutoHachToanConfirm(danhSachTBTPSelectedIds))
    }, [])

    const handleClosePdfConfirm = useCallback(() => {
        dispatch(closePdfConfirm())
    }, [])
    const handleClosePaymentConfirm = useCallback(() => {
        dispatch(closePaymentConfirm())
    }, [])

    const handleClosePrintPdfConfirm = () => {
        set_is_show_print_pdf_confirm(false)

    }
    const handleShowPrintConfirm = () => {
        set_is_show_print_pdf_confirm(true)
    }
    const handleCloseAutoHachToanConfirm = useCallback(() => {
        dispatch(closeAutoHachToanConfirm())
    }, [])

    const handleCloseTBTPSendConfirm = useCallback(() => {
        dispatch(closeTBTPSendConfirm())
    }, [])
    const handleShowTBTPSendConfirm = useCallback(() => {
        dispatch(showTBTPSendConfirm(danhSachTBTPSelectedIds))
    }, [])

    const handleBtnInsertClick = useCallback(() => {
        dispatch(showTBTPDetailModal());
    }, []);

    const handleBtnTaoDotGuiClick = useCallback(() => {
        dispatch(showTBTPDotGuiDetailModal());
    }, []);
    const handleCloseDotGuiDetailModal = useCallback(() => {
        dispatch(closeTBTPDotGuiDetailModal())
    }, [])
    const handleSubmitDotGuiDetailModal = useCallback((data: sf_dotgui_tbtp) => {
        dispatch(saveDotGuiStart(data))
    }, [])

    const onSelectionChanged = useCallback(({ selectedRowKeys }: any) => {
        if (selectedRowKeys !== undefined) {
            dispatch(changeTBTPeSelectionIds(selectedRowKeys))
        }
    }, []);
    const handleConfirmDelete = useCallback(() => {
        dispatch(deleteTBTPStart({
            ids: danhSachTBTPSelectedIds,
            dm_coso_id: dm_coso_id
        }))
    }, [danhSachTBTPSelectedIds]);


    const handleConfirmPdf = useCallback(() => {
        dispatch(createPdfStart({
            sf_tbtp_ids: danhSachTBTPSelectedIds,
            dm_coso_id: dm_coso_id
        }))
    }, [danhSachTBTPSelectedIds]);

    const handleConfirmPayment = () => {
        dispatch(createPaymentStart({
            sf_tbtp_ids: danhSachTBTPSelectedIds,
            sf_hinhthucnopbom_id: hinhThucNopBomId
        }))
    };

    const handleSendSubmit = useCallback(() => {
        dispatch(sendTBTPStart({
            sf_tbtp_ids: danhSachTBTPSelectedIds
        }))
    }, [danhSachTBTPSelectedIds]);

    const handleConfirmAutoHachToan = useCallback(() => {
        dispatch(autoHachToanStart({
            sf_tbtp_ids: danhSachTBTPSelectedIds
        }))
    }, [danhSachTBTPSelectedIds]);

    const handleCloseDetailModal = useCallback(() => {
        dispatch(closeTBTPDetailModal())
        handleRefresh();
    }, [])
    const handleSubmitSuccessDetailModal = useCallback(() => {
        handleRefresh();
        dispatch(closeTBTPDetailModal());
    }, [])

    const handleShowEditModal = useCallback((sf_tbtp: sf_tbtp_viewmodel) => {
        dispatch(showEditModal(sf_tbtp))
    }, [])

    const handleCloseEditModal = useCallback(() => {
        dispatch(closeEditModal())
    }, [])
    //
    const handleShowDotGuiModal = () => {
        setIsShowDotGuiModal(true)
    }
    const handleShowTBTPSendRemindConfirm = () => {
        set_is_show_send_remind_confirm(true)
    }
    const handleCloseTBTPSendRemindConfirm = () => {
        set_is_show_send_remind_confirm(false)
    }
    const handleSendRemind = async () => {
        const request: sf_tbtp_send_remind_rq = {
            dm_coso_id: dm_coso_id,
            sf_tbtp_ids: danhSachTBTPSelectedIds,
            han_nop_nhac_no: han_nop_nhac_no,
            noi_dung_nhac_no: noi_dung_nhac_no,
            lan_nhac_no: lan_nhac_no

        };
        set_IsLoading(true)
        var res = await tbtpApi.send_remind_tbtp(request);
        set_IsLoading(false)
        if (res.is_success) {
            showNotify({ message: "Đã gửi thông báo nhắc nợ thành công", type: "success" });
            set_is_show_send_remind_confirm(false)
        }
        else {
            showNotify({ message: "Không gửi được thông báo nhắc nợ", type: "warning" });
            set_is_show_send_remind_confirm(false)
        }
    };
    const handleBtnPrintClick = async () => {
        const request: sf_tbtp_delete_rq = {
            dm_coso_id: dm_coso_id,
            ids: danhSachTBTPSelectedIds,

        };
        set_IsLoading(true)
        var res = await tbtpApi.print_pdf_file(request);
        set_IsLoading(false)
        if (res.is_success) {
            handlePrint(res.data)
            showNotify({ message: "Đã in thành thành công", type: "success" });
            set_is_show_print_pdf_confirm(false)
        }
        else {
            showNotify({ message: "Không in được file TBTP", type: "warning" });
            set_is_show_print_pdf_confirm(false)
        }
    };
    const handlePrint = (link: string) => {
        window.open(link, '_blank');
    }

    // const columns = useMemo((): any => {
    //     let result: any = []

    //     hinhThucNopBoms.forEach((htnb: any) => {
    //         if (dotGui) {
    //             if (dotGui.sf_hinhthucnopbom_ids.split(',').map(Number).includes(htnb.id)) {
    //                 let sf_khoannop_temp = [...sf_khoannops]
    //                 sf_khoannop_temp = sf_khoannop_temp.filter((x: any) => x.dm_coso_id === dm_coso_id && x.is_tao_trong_tbtp
    //                     && x.sf_hinhthucnopbom_ids.split(',')
    //                         .map(Number)
    //                         .some((id: any) => htnb.id == id)
    //                 )
    //                 const hinhThucNopBomColumn2 = (
    //                     <Column key={createUUID()} caption={`Tổng kết ${htnb.name}`} alignment={"center"}>
    //                         <Column key={createUUID()} dataField={`Tong_tien_dinh_muc_${htnb.id}`} caption={"Phải thu"} format={",##0"} width={120} />,
    //                         <Column key={createUUID()} dataField={`Tong_tien_mien_giam_${htnb.id}`} caption={"Miễn giảm"} format={",##0"} width={120} />,
    //                         <Column key={createUUID()} dataField={`Tong_tien_hoan_tra_${htnb.id}`} caption={"Hoàn trả"} format={",##0"} width={120} />,
    //                         <Column key={createUUID()} dataField={`Tong_tien_thu_bo_sung_${htnb.id}`} caption={"Thu bổ sung"} format={",##0"} width={120} />
    //                         <Column key={createUUID()} dataField={`Tong_tien_phai_nop_${htnb.id}`} caption={"Phải nộp"} headerCellRender={(e) => (
    //                             <span
    //                                 style={{
    //                                     color: "red"
    //                                 }}
    //                             >
    //                                 {e.column.caption}
    //                             </span>
    //                         )}
    //                             width={150}
    //                             cellRender={(e) => (
    //                                 <span
    //                                     style={{
    //                                         color: e.data[`Tong_tien_phai_nop_${htnb.id}`] > 0 ? "red" : "green"
    //                                     }}
    //                                 >
    //                                     {e.text}
    //                                 </span>
    //                             )}
    //                             format={",##0"} />
    //                     </Column>
    //                 );
    //                 result.push(hinhThucNopBomColumn2);
    //             }
    //         }
    //     });
    //     return result;

    // }, [dotGui, sf_khoannops, hinhThucNopBoms])

    const columns = useMemo(() => {
        let result: any = [
            {
                caption: "Trạng thái HS",
                id: "trang_thai",
                dataField: "trang_thai",
                width: "120px",
            },
            {
                caption: "Mã HS",
                id: "ma_hs",
                dataField: "ma_hs",
                width: "80px",
            },
            {
                caption: "Họ tên",
                id: "ho_ten",
                dataField: "ho_ten",
                width: "120px",
                cellRender: (data: any) => (
                    <div style={{ width: "100%", display: "flex" }}>
                        <div style={{ flexGrow: 0, flex: 1 }}>{data.ho_ten}</div>
                    </div>
                ),
            },
            {
                caption: "Nội dung",
                id: "noi_dung",
                dataField: "noi_dung",
                with: "150px",
                cellRender: (data: any) => (
                    <a
                        style={{ fontWeight: "bold", cursor: "pointer", whiteSpace: "pre-wrap", lineHeight: "1rem", color: "#3851E6" }}
                        onClick={() => handleShowEditModal(data)}
                    >
                        {data.noi_dung}
                    </a>
                ),
            },
            {
                caption: "Email người nhận",
                id: "receive_user",
                dataField: "receive_user",
                minWidth: "150px",
                cellRender: (data: any) => {
                    const items = data.receive_user?.split("##") || [];
                    return items.length > 0 && items[0] !== "" ? (
                        <>
                            {items.map((item: any, index: number) => (
                                <p key={index}>{item}</p>
                            ))}
                        </>
                    ) : (
                        <div></div>
                    );
                },
            },
            {
                caption: "Ngày tạo",
                id: "ngay_tao",
                dataField: "ngay_tao",
                width: "80px",
                cellRender: (data: any) =>
                    <>{moment(data.ngay_tao).format("DD/MM/YYYY") == '01/01/0001' ? '' : moment(data.ngay_tao).format("DD/MM/YYYY")}</>
            },
            {
                caption: "Chứng từ",
                columns: [
                    {
                        caption: "Đã tạo CT", id: "is_tao_chung_tu", dataField: "is_tao_chung_tu", width: "80px",
                        cellRender: (data: any) => (
                            <div style={{ display: "flex", justifyContent: "center" }}>
                                <Checkbox
                                    checked={data.is_tao_chung_tu == true}
                                    aria-label="Đã tạo CT"
                                />
                            </div>
                        ),
                    },
                    { caption: "Người tạo", id: "nguoi_tao_chung_tu", dataField: "nguoi_tao_chung_tu", width: "100px" },
                    {
                        caption: "Đã thanh toán", id: "is_da_ghi_nhan_tien", dataField: "is_da_ghi_nhan_tien", width: "100px",
                        cellRender: (data: any) => (
                            <div style={{ display: "flex", justifyContent: "center" }}>
                                <Checkbox
                                    checked={data.is_da_ghi_nhan_tien == true}
                                    aria-label="Đã thanh toán"
                                />
                            </div>
                        ),
                    },
                ],
            },
            {
                caption: "Gửi TBTP",
                columns: [
                    {
                        caption: "File",
                        id: "pdf_file",
                        align: 'center',
                        width: "60px",
                        cellRender: (data: any) =>
                            data.pdf_file ? (
                                <a href={data.pdf_file} target="_blank" style={{ cursor: "pointer", color: "#3c22ff" }}>
                                    <i className="fas fa-file-pdf"></i>
                                </a>
                            ) : (
                                <></>
                            ),
                    },
                    {
                        caption: "Ngày tạo file", id: "ngay_tao_file", dataField: "ngay_tao_file", width: "100px",
                    },
                    {
                        caption: "Ngày gửi", id: "send_time", dataField: "send_time", width: "100px",
                        cellRender: (data: any) =>
                            <>{data.send_time ? moment(data.send_time).format("DD/MM/YYYY HH:mm") : ''}</>
                    },
                    { caption: "Người gửi", id: "send_user", dataField: "send_user", minWidth: "100px" },
                    { caption: "Ghi chú", id: "note", dataField: "note", minWidth: "100px" },
                ],
            },
        ];

        if (dotGui?.sf_hinhthucnopbom_ids) {
            const hinhThucNopBomIds = dotGui.sf_hinhthucnopbom_ids.split(',').map(Number);

            hinhThucNopBoms.forEach((htnb: any) => {
                if (hinhThucNopBomIds.includes(htnb.id)) {
                    result.push({
                        caption: `Tổng kết ${htnb.name}`,
                        align: "center",
                        isAllowFocus: false,
                        columns: [
                            {
                                caption: "Phải thu",
                                id: `Tong_tien_dinh_muc_${htnb.id}`,
                                dataField: `Tong_tien_dinh_muc_${htnb.id}`,
                                width: "120px",
                                isAllowFocus: false,
                                cellRender: (data: any) => (
                                    <span style={{ display: "block", textAlign: "right" }}>
                                        {data[`Tong_tien_dinh_muc_${htnb.id}`]?.toLocaleString()}
                                    </span>
                                ),
                            },
                            {
                                caption: "Miễn giảm",
                                id: `Tong_tien_mien_giam_${htnb.id}`,
                                dataField: `Tong_tien_mien_giam_${htnb.id}`,
                                width: "120px",
                                isAllowFocus: false,
                                cellRender: (data: any) => (
                                    <span style={{ display: "block", textAlign: "right" }}>
                                        {data[`Tong_tien_mien_giam_${htnb.id}`]?.toLocaleString()}
                                    </span>
                                ),
                            },
                            {
                                caption: "Hoàn trả",
                                id: `Tong_tien_hoan_tra_${htnb.id}`,
                                dataField: `Tong_tien_hoan_tra_${htnb.id}`,
                                width: "120px",
                                isAllowFocus: false,
                                cellRender: (data: any) => (
                                    <span style={{ display: "block", textAlign: "right" }}>
                                        {data[`Tong_tien_hoan_tra_${htnb.id}`]?.toLocaleString()}
                                    </span>
                                ),
                            },
                            {
                                caption: "Thu bổ sung",
                                id: `Tong_tien_thu_bo_sung_${htnb.id}`,
                                dataField: `Tong_tien_thu_bo_sung_${htnb.id}`,
                                width: "120px",
                                isAllowFocus: false,
                                cellRender: (data: any) => (
                                    <span style={{ display: "block", textAlign: "right" }}>
                                        {data[`Tong_tien_thu_bo_sung_${htnb.id}`]?.toLocaleString()}
                                    </span>
                                ),
                            },
                            {
                                caption: "Phải nộp",
                                id: `Tong_tien_phai_nop_${htnb.id}`,
                                dataField: `Tong_tien_phai_nop_${htnb.id}`,
                                width: "150px",
                                isAllowFocus: false,
                                cellRender: (data: any) => {
                                    const value = data[`Tong_tien_phai_nop_${htnb.id}`] ?? 0;
                                    return (
                                        <span style={{ color: value > 0 ? "red" : "green", display: "block", textAlign: "right" }}>
                                            {value.toLocaleString()}
                                        </span>
                                    );
                                },
                            },
                        ],
                    });
                }
            });
        }
        return result;
    }, [dotGui, sf_khoannops, hinhThucNopBoms]);
    const handleExportAll = async () => {
        const request: sf_tbtp_select_rq_dotgui = {
            nam_hoc: nam_hoc,
            dm_he_id: dm_he_id,
            dm_khoi_id: dm_khoi_id,
            dm_truong_id: dm_truong_id,
            sf_dotgui_tbtp_id: dotGuiId
        };
        set_IsLoading(true)
        var res = await tbtpApi.select_export(request);
        if (res.is_success) {
            // Tạo worksheet từ dữ liệu gốc
            const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(res.data);
            // Tạo workbook và thêm worksheet vào
            const workbook: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "ReportData");
            set_IsLoading(false);
            // Xuất workbook ra file Excel
            XLSX.writeFile(workbook, "ThongBaoHocPhi.xlsx");
        }
        else {
            showNotify({ message: "Export thất bại", type: "warning" });
        }

    }
    return (
        <React.Fragment>
            <Box sx={{ p: 2 }}>
                <Transition animation="scale"
                    visible={true}
                    duration={100}
                    transitionOnMount={true}>
                    <Box className="row">
                        <Box className="col-md-12 page-toolbar">
                            <Toolbar className="bg-background">
                                <Item location="before">
                                    <ComboboxTruong
                                        value={dm_truong_id}
                                        onValueChanged={(value) => {
                                            dispatch(changeTBTPTruong(value))
                                        }}
                                    />
                                </Item>
                                <Item location="before">
                                    <ComboboxKhoi
                                        dm_truong_id={dm_truong_id}
                                        value={dm_khoi_id}
                                        width={110}
                                        onValueChanged={(value) => {
                                            dispatch(changeTBTPKhoi(value || 0))
                                        }}
                                        isShowClearButton={true}

                                    />
                                </Item>
                                <Item location="before">
                                    <ComboboxHe
                                        dm_khoi_id={dm_khoi_id}
                                        dm_truong_id={dm_truong_id}
                                        value={dm_he_id}
                                        onValueChanged={(value) => {
                                            dispatch(changeTBTPHe(value || 0))
                                        }}
                                        isShowClearButton={true}

                                    />

                                </Item>

                                <Item location="before">
                                    {/* <TagBoxTrangThaiHocSinh
                                            width={200}
                                            stylingMode="filled"
                                            value={selectedStatusStudent}
                                            onValueChanged={(id: any, data: any) => {
                                                setSelectedStatusStudent(id)
                                            }}

                                        /> */}
                                    <SelectBoxTrangThaiHocSinhMultiple
                                        selectedValue={selectedStatusStudent}
                                        onSelectionChanged={(ids) => {
                                            setSelectedStatusStudent(ids)
                                        }}
                                        maxWidth={300}
                                        isShowClearBtn={true}
                                    />
                                </Item>
                                <Item location="before">
                                    <CmbDotGuiTBTP
                                        isShowClearButton={true}
                                        width={150}
                                        stylingMode="filled"
                                        value={dotGuiId}
                                        onValueChanged={(id, data) => {
                                            setDotGuiId(id || 0)
                                            setDotGui(data)
                                        }}
                                    />
                                </Item>
                            </Toolbar>
                        </Box>
                        <Box className="col-md-12" sx={{ mt: 2 }}>
                            <DataTable
                                titleComponent={
                                    <TBTPStatusSummary
                                        sf_tbtp_viewmodels={dataSourceFilter}
                                        hinhThucNopBomSelectedId={hinhThucNopBomSelectedId}
                                        sendFilterSelectedId={sendFilterValue}
                                        onHinhThucNopBomChanged={setHinhThucNopBomSelectedId}
                                        onSendFilterChanged={setSendFilterValue}
                                    />
                                }
                                data={dataSource}
                                // emptyComponent={
                                //     <Box
                                //         sx={{
                                //             display: "flex",
                                //             flexDirection: "column",
                                //             alignItems: "center",
                                //             justifyContent: "center",
                                //             minHeight: "60vh",
                                //             textAlign: "center"
                                //         }}
                                //     >
                                //         <img
                                //             src="https://true-north-school.s3.cloudfly.vn/fee/datagrid_empty"
                                //             alt="Không có dữ liệu"
                                //             style={{ width: "60px", height: "60px", marginBottom: "5px" }}
                                //         />
                                //         <Box sx={{ fontSize: "13px", fontWeight: "400", color: "#8C8C8C" }}>
                                //             Chưa có dữ liệu thông báo thu phí
                                //         </Box>
                                //     </Box>
                                // }
                                selection={{
                                    mode: "multiple",
                                    keyExpr: "id",
                                    selectedRowKeys: danhSachTBTPSelectedIds,
                                    onSelectionChanged: (keys) => setDanhSachTBTPSelectedIds(keys),
                                }}
                                searchEnable
                                filterRow={{
                                    enable: true
                                }}
                                actionComponent={
                                    <Box sx={{ display: "flex" }}>

                                        <Button sx={{ ml: 2 }} variant="default" size="medium" leadingVisual={SyncIcon} onClick={handleRefresh}>Làm mới</Button>
                                        <Button sx={{ ml: 2 }} variant="primary" size="medium" leadingVisual={ProjectRoadmapIcon} onClick={() => {
                                            setIsShowAlphaDetail(true)
                                        }}>Report</Button>
                                        <Button sx={{ ml: 2 }} variant="primary" size="medium" leadingVisual={PlusIcon} onClick={handleBtnInsertClick}>Tạo TBTP</Button>

                                        <ActionMenu >
                                            <ActionMenu.Button style={{ height: 32, marginLeft: 7 }}>
                                                Thao tác
                                            </ActionMenu.Button>
                                            <ActionMenu.Overlay width="auto">
                                                <ActionList showDividers>
                                                    <ActionList.Group>
                                                        <ActionList.Item
                                                            onSelect={handleShowDeleteConfirm}
                                                            disabled={!isAccessibleDelete}
                                                        >
                                                            <ActionList.LeadingVisual >
                                                                <TrashIcon />
                                                            </ActionList.LeadingVisual>
                                                            <Text text={`Xóa ${danhSachTBTPSelectedIds.length} TBTP`} />
                                                        </ActionList.Item>
                                                        <ActionList.Item
                                                            onSelect={handleShowPdfConfirm}
                                                            disabled={!isAccessiblePdf}
                                                        >
                                                            <ActionList.LeadingVisual >
                                                                <FileIcon />
                                                            </ActionList.LeadingVisual>
                                                            <Text text={`Tạo ${danhSachTBTPSelectedIds.length} file PDF`} />
                                                        </ActionList.Item>
                                                        <ActionList.Item
                                                            onSelect={handleShowPrintConfirm}
                                                            disabled={!isAccessiblePdf}
                                                        >
                                                            <ActionList.LeadingVisual >
                                                                < LinkIcon />
                                                            </ActionList.LeadingVisual>
                                                            <Text text={`In ${danhSachTBTPSelectedIds.length} file PDF`} />
                                                        </ActionList.Item>
                                                        <ActionList.Item
                                                            onSelect={handleShowPaymentConfirm}
                                                            disabled={!isAccessiblePdf}
                                                        >
                                                            <ActionList.LeadingVisual >
                                                                <BellIcon />
                                                            </ActionList.LeadingVisual>
                                                            <Text text={`Tạo ${danhSachTBTPSelectedIds.length} chứng từ`} />
                                                        </ActionList.Item>

                                                        <ActionList.Item
                                                            onSelect={handleShowTBTPSendConfirm}
                                                            disabled={!isAccessiblePdf}
                                                        >
                                                            <ActionList.LeadingVisual >
                                                                <ArrowUpRightIcon />
                                                            </ActionList.LeadingVisual>
                                                            <Text text={`Gửi ${danhSachTBTPSelectedIds.length} TBTP`} />
                                                        </ActionList.Item>
                                                        <ActionList.Item
                                                            onSelect={handleShowAutoHachToanConfirm}
                                                            disabled={false}
                                                        >
                                                            <ActionList.LeadingVisual >
                                                                <PlusIcon />
                                                            </ActionList.LeadingVisual>
                                                            <Text text={`Thêm quyết toán`} />
                                                        </ActionList.Item>
                                                        <ActionList.Item
                                                            onSelect={handleExport}
                                                            disabled={false}
                                                        >
                                                            <ActionList.LeadingVisual >
                                                                <DownloadIcon />
                                                            </ActionList.LeadingVisual>
                                                            <Text text={`Export từng đợt gửi`} />
                                                        </ActionList.Item>
                                                        <ActionList.Item
                                                            onSelect={handleExportAll}
                                                            disabled={false}
                                                        >
                                                            <ActionList.LeadingVisual >
                                                                <DownloadIcon />
                                                            </ActionList.LeadingVisual>
                                                            <Text text={`Export tất cả đợt gửi`} />
                                                        </ActionList.Item>
                                                    </ActionList.Group>

                                                </ActionList>
                                            </ActionMenu.Overlay>
                                        </ActionMenu>
                                    </Box>
                                }
                                columns={columns}
                                paging={{
                                    enable: true,
                                    pageSizeItems: [50, 100, 200, 500]
                                }}
                            />
                        </Box>

                    </Box>
                </Transition>
                {is_show_detail_modal && <TBTPCreateModal
                    filter={{
                        nam_hoc,
                        dm_khoi_id,
                        dm_he_id,
                        dm_truong_id
                    }}
                    onSucess={() => {
                        handleReload();
                        dispatch(closeTBTPDetailModal());
                    }}
                    onClose={() => {
                        handleReload();
                        handleCloseDetailModal();
                    }}
                    sf_dotgui_tbtp={dotGui}
                />}

                {is_show_delete_confirm &&
                    <AnimationConfirm
                        onClose={handleCloseDeleteConfirm}
                        onConfirmed={handleConfirmDelete}
                        text={`Bạn có chắc chắn muốn xóa ${danhSachTBTPSelectedIds.length} TBTP đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_deleting}
                        text_close_button="Không xóa"
                        text_confirm_button="Xóa TBTP"
                        type='danger'
                        animationOf='#btnDelete'

                    />
                }
                {is_show_pdf_confirm &&
                    <AnimationConfirm
                        onClose={handleClosePdfConfirm}
                        onConfirmed={handleConfirmPdf}
                        text={`Bạn có chắc chắn muốn tạo mới ${danhSachTBTPSelectedIds.length} file PDF cho các TBTP đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_saving}
                        text_close_button="Không tạo"
                        text_confirm_button="Tạo file Pdf"
                        type='success'
                        icon='fas fa-file-pdf'
                        animationOf='#btnPdf'

                    />
                }

                {is_show_payment_confirm &&

                    <DefaultPopup title="Tạo chứng từ" onClose={handleClosePaymentConfirm}>
                        <div className="pop-up-form">
                            <div className="col-md-12 padding-top-1em">
                                <h4>Chọn hình thức nộp bên dưới để tạo chứng từ</h4>
                                <CmbHinhThucNopBom
                                    isShowClearButton={true}
                                    width={330}
                                    stylingMode="outlined"
                                    value={hinhThucNopBomId}
                                    onValueChanged={(id) => {
                                        // console.log(id)
                                        setHinhThucNopBomId(id)
                                    }}
                                    dmCoSoId={dm_coso_id}
                                    sf_hinhthucnopbom_dotgui={dotGui?.sf_hinhthucnopbom_ids}
                                />

                            </div>
                        </div>
                        <Box className="pop-up-actions" sx={{ mr: 1, display: 'flex', justifyContent: 'flex-end' }}>
                            <Button sx={{ ml: 2 }} variant="default" size="small" leadingVisual={XIcon} onClick={handleClosePaymentConfirm}>Không tạo</Button>
                            <Button sx={{ ml: 2 }} variant="primary" size="small" leadingVisual={CheckIcon} isLoading={status === PageBaseStatus.is_saving} onClick={handleConfirmPayment}>Tạo chứng từ</Button>
                        </Box>
                    </DefaultPopup>
                }
                {is_show_print_pdf_confirm &&
                    <DefaultPopup title="Thông báo">
                        <div className="pop-up-form">
                            <div className="col-md-12 padding-top-1em">
                                <h4>{`Bạn có chắc chắn muốn in ${danhSachTBTPSelectedIds.length} file PDF đã chọn ?`}</h4>
                            </div>
                        </div>
                        <Box className="pop-up-actions" sx={{ mr: 1, display: 'flex', justifyContent: 'flex-end' }}>
                            <Button sx={{ ml: 2 }} variant="default" size="small" leadingVisual={XIcon} onClick={handleClosePrintPdfConfirm}>Không in</Button>
                            <Button sx={{ ml: 2 }} variant="primary" size="small" leadingVisual={CheckIcon} isLoading={isLoading} onClick={handleBtnPrintClick}>In PDF file</Button>
                        </Box>
                    </DefaultPopup>
                }
                {is_show_auto_hachtoan_confirm &&
                    <AnimationConfirm
                        onClose={handleCloseAutoHachToanConfirm}
                        onConfirmed={handleConfirmAutoHachToan}
                        text={`Bạn có chắc chắn muốn tạo thêm tất cả khoản quyết toán của từng học sinh vào từng TBTP đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_saving}
                        text_close_button="Không thêm"
                        text_confirm_button="Thêm tất cả"
                        type='success'
                        icon='fas fa-check-double'
                        animationOf='#btnAutoHachToan'

                    />
                }
                {is_show_send_remind_confirm &&

                    <DefaultPopup title="Gửi thông báo nhắc nợ">
                        <div className="pop-up-form">
                            <div className="col-md-12 padding-top-1em">
                                <h4>Chọn hạn nộp bên dưới để gửi TB nhắc nợ tới PHHS cho các TBTP đã chọn</h4>
                                <DateBox
                                    width={150}
                                    className={"margin-right-10"}
                                    value={han_nop_nhac_no}
                                    onValueChange={(value) => {
                                        const han_nop =
                                            moment(value).format("YYYY-MM-DD");
                                        if (han_nop !== han_nop_nhac_no) {
                                            set_han_nop_nhac_no(han_nop);
                                        }
                                    }}
                                    displayFormat={"dd-MM-yyyy"}
                                    dateSerializationFormat={"yyyy-MM-ddTHH:mm:ss"}
                                    labelMode={"static"}
                                    label={"Hạn nộp"}
                                    readOnly={false}
                                />
                                <br />
                                <label>Nội dung</label>
                                <TextBox value={noi_dung_nhac_no}
                                    stylingMode="filled"
                                    className="font-size-14"
                                    width={330}
                                    onValueChanged={(e) => {
                                        set_noi_dung_nhac_no(e.value || '')
                                    }}
                                />
                                <br />
                                <label>Lần nhắc nợ</label>
                                <NumberBox value={lan_nhac_no}
                                    stylingMode="filled"
                                    className="font-size-14"
                                    width={330}
                                    onValueChanged={(e) => {
                                        set_lan_nhac_no(e.value)
                                    }}
                                />
                            </div>
                        </div>
                        <Box className="pop-up-actions" sx={{ mr: 1, display: 'flex', justifyContent: 'flex-end' }}>
                            <Button sx={{ ml: 2 }} variant="default" size="small" leadingVisual={XIcon} onClick={handleCloseTBTPSendRemindConfirm}>Không gửi</Button>
                            <Button sx={{ ml: 2 }} variant="primary" size="small" leadingVisual={CheckIcon} isLoading={isLoading} onClick={handleSendRemind}>Gửi TB nhắc nợ</Button>
                        </Box>
                    </DefaultPopup>
                }
                {is_show_send_confirm &&
                    <AnimationConfirm
                        onClose={handleCloseTBTPSendConfirm}
                        onConfirmed={handleSendSubmit}
                        text={`Bạn có chắc chắn muốn gửi TBTP tới PHHS cho các TBTP đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_saving}
                        text_close_button="Không gửi"
                        text_confirm_button="Gửi TBTP"
                        type='success'
                        icon='fas fa-paper-plane'
                        animationOf='#btnSendPdf'

                    />
                }
                {is_show_edit_modal && sf_tbtp_editing &&
                    <TBTPEditModal sf_tbtp={sf_tbtp_editing}
                        onCancel={handleCloseEditModal}
                        isReadOnly={sf_tbtp_editing.is_sended || false}
                    // sf_hachtoan_congnos_pending={sf_tbtp_editing.sf_hachtoan_congnos}

                    />}
                {isShowGhiChuModal && <EditGhiChuTBTPModal
                    dm_truong={dmTruongSelected}
                    dm_he={dmHeSelected}
                    animationOf='btnEditGhiChu'
                    onClose={() => { setIsShowGhiChuModal(false) }}
                />}

                {isShowAlphaDetail && <AlphaReportModal
                    onClose={() => { setIsShowAlphaDetail(false) }}
                    dm_truong_id={dm_truong_id}
                    dm_khoi_id={dm_khoi_id}
                    dm_he_id={dm_he_id}
                    nam_hoc={nam_hoc}
                    sf_dotgui_tbtp_id={dotGuiId}
                />}
            </Box>
        </React.Fragment>
    );
}
export default TBTPPage;