
import { ActionList, ActionMenu } from '@primer/react';
import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { dm_truong } from '../../models/response/category/dm_truong';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
type ComboboxTruongProps = {
    isReadonly?: boolean,
    value?: number,
    onValueChanged: (id: number, data?: dm_truong) => void,
    className?: string,
    isShowClearButton?: boolean,
    preText?: string,
    stylingMode?: "outlined" | "filled" | "underlined"
}
const ComboboxTruong = (props: ComboboxTruongProps) => {
    const { dm_coso_id } = useSelector((x: RootState) => x.common);
    const { translate } = useCommonContext();
    const dispatch = useDispatch();
    const categorySource = useSelector((state: RootState) => state.categorySource);

    useEffect(() => {
        if (categorySource.dm_truongs.length === 0) dispatch(actions.categorySource.loadDmTruongStart());
    }, []);

    const dm_truongs: dm_truong[] = useMemo(() => {
        return categorySource.dm_truongs.filter((x: any) => x.dm_coso_id === dm_coso_id && x.viet_tat !== 'na').map(x => ({ ...x }));
    }, [categorySource.dm_truongs, dm_coso_id]);
    const selectedData = useMemo(() => {
        if (props.value && dm_truongs) {
            return dm_truongs.find(x => x.id == props.value)
        }
        return undefined
    }, [dm_truongs, props.value])
    return (
        // <Select onChange={(e) => {
        //     const dm_truong_id: number = e.currentTarget.value ? parseInt(e.currentTarget.value) : 0
        //     props.onValueChanged(dm_truong_id, source.find(x => x.id == dm_truong_id))
        // }}
        //     value={`${props.value ?? 0}`}
        //     placeholder={translate("Chọn trường")}
        // >
        //     {source.map(x => {
        //         return (
        //             <Select.Option key={x.id} value={`${x.id}`}>{x.viet_tat}</Select.Option>
        //         );
        //     })}
        // </Select>
        <div style={{ width: '100%' }}>
            <ActionMenu>
                <ActionMenu.Button
                    aria-label="Select school year"
                    style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between'
                    }}
                >
                    {selectedData ? selectedData.ten_truong : "Chọn cấp học"}
                </ActionMenu.Button>
                <ActionMenu.Overlay width="small">
                    <ActionList selectionVariant="single">
                        {props.isShowClearButton &&
                            <ActionList.Item key={0} selected={props.value != undefined && 0 === props.value}
                                onSelect={() => {
                                    props.onValueChanged(0)
                                }}
                            >
                                {"Chọn cấp học"}
                            </ActionList.Item>
                        }
                        {dm_truongs && dm_truongs.map((item, index) => {
                            return (
                                <ActionList.Item key={item.id} selected={props.value != undefined && item.id === props.value}
                                    onSelect={() => {
                                        props.onValueChanged(item.id)
                                    }}
                                >
                                    {item.ten_truong}
                                </ActionList.Item>
                            );
                        })}
                    </ActionList>
                </ActionMenu.Overlay>
            </ActionMenu>
        </div>
    );

}
export { ComboboxTruong };

