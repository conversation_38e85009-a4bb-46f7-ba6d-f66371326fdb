import { IconButton, IconButtonProps } from "@primer/react";

type IIconButtonProps = IconButtonProps & {
    isLoading?: boolean,
    apiAuthorized?: string,
    apiAuthorizedMethod?: "GET" | "PUT" | "POST" | "DELETE",
    iconClass?: string
}
const MyIconButton = (props: IIconButtonProps) => {

    return (
        <>
            <IconButton {...props} />
        </>

    );
};
export default MyIconButton