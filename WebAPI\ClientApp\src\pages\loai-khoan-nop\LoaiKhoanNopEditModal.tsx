import { Box, FormControl } from '@primer/react';
import { useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import Button from '../../components/ui/button';
import Modal from '../../components/ui/modal';
import ModalActions from '../../components/ui/modal/ModalActions';
import Text from '../../components/ui/text';
import TextInput from '../../components/ui/text-input';
import { sf_loaikhoannop } from '../../models/response/category/sf_loaikhoannop';
import { RootState } from '../../state/reducers';
type Props = {
    title?: string,
    sf_loaikhoannop?: sf_loaikhoannop,
    onClose: () => void,
    onSubmit: (data: sf_loaikhoannop) => void,
    is_saving?: boolean,
    animationOf: string
}
const LoaiKhoanNopEditModal = (props: Props) => {
    const { dm_coso_id } = useSelector((x: RootState) => x.common);

    const { register, handleSubmit, setValue, watch, control, formState: { errors } } = useForm<sf_loaikhoannop>({
        defaultValues: props.sf_loaikhoannop ?? {}
    });
    const onSubmit = async (data: any) => {
        props.onSubmit({
            ...data,
            dm_coso_id: dm_coso_id,
        })
    }
    { { } }
    return (
        <Modal isOpen
            title={props.sf_loaikhoannop ? "Cập nhật" : "Thêm mới"}
            onClose={() => {
                props.onClose()
            }}
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box sx={{ display: "grid", gap: 2 }}>
                    <FormControl>
                        <FormControl.Label><Text text='Tên loại khoản nộp (vi)' /></FormControl.Label>
                        <TextInput block
                            name="loai_khoan_nop"
                            register={register}
                            errors={errors}
                            required
                            validateMessage={'Vui lòng điền tên loại khoản nộp (vi)'}
                        />

                    </FormControl>
                    <FormControl>
                        <FormControl.Label><Text text='Tên loại khoản nộp (en)' /></FormControl.Label>
                        <TextInput block
                            name="loai_khoan_nop_en"
                            register={register}
                            errors={errors}
                            required = {false}
                        />
                    </FormControl>
                </Box>

                <ModalActions>
                    <Button text='Đóng' onClick={() => {
                        props.onClose();
                    }} />
                    <Button text='Cập nhật' variant='primary' type='submit'
                    />
                </ModalActions>
            </form>

        </Modal>
    );
};

export default LoaiKhoanNopEditModal;