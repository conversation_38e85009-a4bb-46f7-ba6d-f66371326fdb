﻿import {
    PlusIcon,
    SyncIcon,
    TrashIcon
} from "@primer/octicons-react";
import { Box, Checkbox } from '@primer/react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { Transition } from 'semantic-ui-react';
import { AnimationConfirm } from '../../components/confirm';
import Button from '../../components/ui/button';
import DataTable from "../../components/ui/data-table";
import Text from "../../components/ui/text";
import { PageBaseStatus } from '../../models/pageBaseStatus';
import { sf_khoannop_group } from '../../models/response/category/sf_khoannop_group';
import { changeKhoanNopGroupSelectionIds, closeKhoanNopGroupDetailModal, deleteKhoanNopGroupStart, loadKhoanNopGroupStart, saveKhoanNopGroupStart, showKhoanNopGroupDeleteConfirm, showKhoanNopGroupDetailModal } from '../../state/actions/khoanNopGroupActions';
import { loadNhomKhoanNopStart } from '../../state/actions/nhomKhoanNopActions';
import { RootState } from '../../state/reducers';
import KhoanNopGroupEditModal from "./KhoanNopGroupEditModal";

const KhoanNopGroupPage = () => {
    const [danhSachKhoanNopGroupSelectedIds, setDanhSachKhoanNopGroupSelectedIds] = useState<number[]>([]);

    const state = useSelector((state: RootState) => state.khoanNopGroup)
    const { dm_coso_id } = useSelector((state: RootState) => state.common)
    const sf_nhomkhoannops = useSelector((x: RootState) => x.nhomKhoanNop.sf_nhomkhoannops)
    const dispatch = useDispatch();
    const grid = useRef<any>();
    const {
        status,
        sf_khoannop_groups,
        sf_khoannop_group_selected_ids,
        sf_khoannop_group_editing,
        is_show_detail_modal,
        is_show_delete_confirm
    } = state;

    const dataSource = useMemo(() => {
        return sf_khoannop_groups.filter(x => x.dm_coso_id === dm_coso_id)
    }, [sf_khoannop_groups, dm_coso_id])

    useEffect(() => {
        dispatch(loadKhoanNopGroupStart())
    }, [])

    useEffect(() => {
        if (sf_nhomkhoannops.length === 0) dispatch(loadNhomKhoanNopStart())
    }, [])


    const handleRefresh = useCallback(() => {
        dispatch(loadKhoanNopGroupStart());
        onSelectionChanged([])
        setDanhSachKhoanNopGroupSelectedIds([])
    }, [])

    const handleCloseDeleteConfirm = useCallback(() => {
        dispatch(showKhoanNopGroupDeleteConfirm(false))
    }, [])

    const handleShowDeleteConfirm = useCallback(() => {
        dispatch(showKhoanNopGroupDeleteConfirm(true))
    }, [])

    const handleBtnInsertClick = useCallback(() => {
        dispatch(showKhoanNopGroupDetailModal());
    }, []);

    const handleBtnEditClicked = (data: sf_khoannop_group) => {
        dispatch(showKhoanNopGroupDetailModal(data));
    }
    const handleExport = useCallback(() => {
        if (grid && grid.current && grid.current.instance) {
            grid.current.instance.exportToExcel();
        }
    }, [grid])
    const onSelectionChanged = ({ selectedRowKeys }: any) => {
        dispatch(changeKhoanNopGroupSelectionIds(selectedRowKeys))

    };

    const handleConfirmDelete = useCallback(() => {
        dispatch(deleteKhoanNopGroupStart(danhSachKhoanNopGroupSelectedIds))
        setDanhSachKhoanNopGroupSelectedIds([])
    }, [danhSachKhoanNopGroupSelectedIds]);

    const handleCloseDetailModal = useCallback(() => {
        dispatch(closeKhoanNopGroupDetailModal())
    }, [])
    const handleSubmitDetailModal = useCallback((data: sf_khoannop_group) => {
        dispatch(saveKhoanNopGroupStart(data))
        dispatch(closeKhoanNopGroupDetailModal())

    }, [])
    return (
        <React.Fragment>
            <Box sx={{ p: 2, pt: 0 }}>
                <Transition animation="scale"
                    visible={true}
                    duration={100}
                    transitionOnMount={true}>
                    <Box>
                        <Box sx={{ m: 2 }}>
                            <DataTable
                                height="calc(100vh - 240px)"
                                titleComponent={<Text text="Danh sách loại khoản nộp" sx={{ fontSize: 16, fontWeight: 600 }} />}
                                data={dataSource}
                                emptyComponent={<Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        minHeight: "60vh",
                                        textAlign: "center"
                                    }}
                                >
                                    <img
                                        src="https://true-north-school.s3.cloudfly.vn/fee/datagrid_empty"
                                        alt="Không có dữ liệu"
                                        style={{ width: "60px", height: "60px", marginBottom: "5px" }}
                                    />
                                    <Box sx={{ fontSize: "13px", fontWeight: "400", color: "#8C8C8C" }}>
                                        Chưa có dữ liệu nhóm khoản nộp TBTP
                                    </Box>
                                </Box>
                                }
                                selection={{
                                    mode: "multiple",
                                    keyExpr: "id",
                                    selectedRowKeys: danhSachKhoanNopGroupSelectedIds,
                                    onSelectionChanged(keys) {
                                        setDanhSachKhoanNopGroupSelectedIds(keys)
                                    },
                                }}
                                exportEnable
                                filterRow={{
                                    enable: true
                                }}
                                actionComponent={
                                    <Box sx={{
                                        display: "flex"
                                    }}>
                                        <Button sx={{ ml: 2 }} variant="default" size="small" leadingVisual={SyncIcon} onClick={handleRefresh}>Làm mới</Button>
                                        <Button sx={{ ml: 2 }} variant="primary" size="small" leadingVisual={PlusIcon} onClick={handleBtnInsertClick}>Thêm mới</Button>
                                        {/* <Button sx={{ ml: 2 }} leadingVisual={DownloadIcon} variant="default" size="medium" onClick={handleExport}>Xuất excel</Button> */}
                                        {danhSachKhoanNopGroupSelectedIds && danhSachKhoanNopGroupSelectedIds.length > 0 &&
                                            <Button sx={{ ml: 2 }} leadingVisual={TrashIcon} variant="danger" size="small" onClick={handleShowDeleteConfirm}>Xóa {danhSachKhoanNopGroupSelectedIds.length} khoản nộp TBTP</Button>
                                        }
                                    </Box>
                                }
                                columns={[
                                    {
                                        caption: "Sửa",
                                        id: "edit",
                                        width: "50px",
                                        align: "center",
                                        isAllowFocus: false,
                                        cellRender: useCallback((data: any) => (
                                            <a id={`btnEdit${data.id}`} style={{ cursor: "pointer", color: "#3c22ff" }}>
                                                <i onClick={() => handleBtnEditClicked(data)} className="cmd-edit-icon fas fa-edit"></i>
                                            </a>
                                        ), [handleBtnEditClicked]),
                                    },
                                    {
                                        caption: "Mã nhóm khoản nộp TBTP",
                                        id: "ma_khoan_nop_group",
                                        dataField: "ma_khoan_nop_group",
                                        isAllowFocus: false,
                                    },
                                    {
                                        caption: "Tên nhóm khoản nộp TBTP",
                                        id: "ten_khoan_nop_group",
                                        dataField: "ten_khoan_nop_group",
                                        isAllowFocus: false,
                                    },
                                    // {
                                    //     caption: "Nhóm khoản nộp",
                                    //     id: "nhom_khoan_nop",
                                    //     dataField: "nhom_khoan_nop",
                                    //     isAllowFocus: false,
                                    //     cellRender: (data: any) => {
                                    //         const obj = sf_nhomkhoannops.find(x => x.id === data.sf_nhomkhoannop_id)
                                    //         return <>
                                    //             {obj?.nhom_khoan_nop}
                                    //         </>
                                    //     },
                                    // },
                                    {
                                        caption: "Kích hoạt",
                                        id: "is_active",
                                        dataField: "is_active",
                                        isAllowFocus: false,
                                        cellRender: (data: any) => (
                                            <div style={{ display: "flex", justifyContent: "center" }}>
                                                <Checkbox
                                                    checked={data.is_active === true}
                                                    aria-label="Kích hoạt"
                                                />
                                            </div>
                                        ),
                                    },
                                    {
                                        caption: "Dịch vụ",
                                        id: "is_dichvu",
                                        dataField: "is_dichvu",
                                        isAllowFocus: false,
                                        cellRender: (data: any) => (
                                            <div style={{ display: "flex", justifyContent: "center" }}>
                                                <Checkbox
                                                    checked={data.is_dichvu === true}
                                                    aria-label="Dịch vụ"
                                                />
                                            </div>
                                        ),
                                    },
                                ]}
                                paging={{
                                    enable: true,
                                    pageSizeItems: [50, 100, 200, 500]
                                }}
                            />
                            {is_show_detail_modal && <KhoanNopGroupEditModal
                                title={sf_khoannop_group_editing ? `Cập nhật` : "Thêm mới"}

                                sf_khoannop_group={sf_khoannop_group_editing}
                                onClose={handleCloseDetailModal}
                                onSubmit={handleSubmitDetailModal}
                                is_saving={status === PageBaseStatus.is_saving}
                                animationOf={sf_khoannop_group_editing ? `#btnEdit${sf_khoannop_group_editing.id}` : "#btnInsert"}
                            />
                            }
                        </Box>

                    </Box>
                </Transition>
                {is_show_delete_confirm &&
                    <AnimationConfirm
                        onClose={handleCloseDeleteConfirm}
                        onConfirmed={handleConfirmDelete}
                        text={`Bạn có chắc chắn muốn xóa ${danhSachKhoanNopGroupSelectedIds.length} dòng đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_deleting}
                        text_close_button="Không xóa"
                        text_confirm_button="Xóa"
                        type='danger'
                        animationOf='#btnDelete'

                    />
                }
            </Box>
        </React.Fragment>
    );
}
export default KhoanNopGroupPage;