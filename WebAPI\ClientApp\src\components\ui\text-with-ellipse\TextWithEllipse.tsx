import { Box, TextProps } from '@primer/react';
import React, { useState } from 'react';
interface ITextWithEllipseProps extends TextProps {
    text: string,
    lineNumber?: number,
    allowToggleText?: boolean
}
const TextWithEllipse = (props: ITextWithEllipseProps) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const { text, lineNumber } = props;
    const toggleText = () => {
        if (props.allowToggleText || props.allowToggleText === undefined)
            setIsExpanded((prev) => !prev);
    };
    return (
        <Box sx={{ width: "100%", maxWidth: "100%" }}>
            {!isExpanded ? (
                <Box onClick={toggleText}
                    style={{
                        display: "-webkit-box",
                        WebkitLineClamp: lineNumber,
                        WebkitBoxOrient: "vertical",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        cursor: "pointer",
                        wordBreak: "break-word",
                        width: "100%",
                        maxWidth: "100%",
                    }}
                    sx={{
                        ...props.sx,
                    }}
                >
                    {text}
                </Box>
            ) : (
                <Box onClick={toggleText} sx={{ whiteSpace: "pre-line" }}>
                    {text}
                </Box>
            )}
        </Box>
    );
};

export default TextWithEllipse;