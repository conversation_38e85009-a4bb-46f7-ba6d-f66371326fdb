
import { ActionList, ActionMenu, Select } from '@primer/react';
import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCommonContext } from '../../contexts/common';
import { dm_khoi } from '../../models/response/category/dm_khoi';
import { actions } from '../../state/actions/actionsWrapper';
import { RootState } from '../../state/reducers';
type ComboboxKhoiProps = {
	isReadonly?: boolean;
	value?: number;
	onValueChanged: (id: number, data?: dm_khoi) => void;
	className?: string;
	isShowClearButton?: boolean;
	preText?: string;
	width?: string | number;
	dm_truong_id?: number;
	stylingMode?: 'outlined' | 'filled' | 'underlined';
	size?: "small" | "medium" | "large" 
};
const ComboboxKhoi = (props: ComboboxKhoiProps) => {
	const { dm_khois, dm_truong_khoi_hes } = useSelector((state: RootState) => state.categorySource);
	const { language } = useSelector((x: RootState) => x.common);
	const dispatch = useDispatch();
	useEffect(() => {
		if (dm_khois.length === 0) dispatch(actions.categorySource.loadKhoiStart());
	}, []);
	const { translate } = useCommonContext();
	const source: dm_khoi[] = useMemo(() => {

		if (props.dm_truong_id) {
			return dm_khois.filter((x) => x.dm_truong_id == props.dm_truong_id);
		}
		return [];
	}, [dm_khois, dm_truong_khoi_hes, props.dm_truong_id]);
	const selectedData = useMemo(() => {
		if (props.value && source) {
			return source.find(x => x.id == props.value)
		}
		return undefined
	}, [source, props.value])
	return (

		<div style={{ width: '100%' }}>
			<ActionMenu>
				<ActionMenu.Button
					aria-label="Select school year"
					size={props.size}
					style={{
						width: '100%',
						display: 'flex',
						justifyContent: 'space-between'
					}}
				>
					{selectedData ? selectedData.ten_khoi : "Chọn khối"}
				</ActionMenu.Button>
				<ActionMenu.Overlay width="small">
					<ActionList selectionVariant="single">
						{props.isShowClearButton &&
							<ActionList.Item key={0} selected={props.value != undefined && 0 === props.value}
								onSelect={() => {
									props.onValueChanged(0)
								}}
							>
								{"Chọn khối"}
							</ActionList.Item>
						}
						{source && source.map((item, index) => {
							return (
								<ActionList.Item key={item.id} selected={props.value != undefined && item.id === props.value}
									onSelect={() => {
										props.onValueChanged(item.id)
									}}
								>
									{item.ten_khoi}
								</ActionList.Item>
							);
						})}
					</ActionList>
				</ActionMenu.Overlay>
			</ActionMenu>
		</div>
	);
};

export default ComboboxKhoi;
