import React from 'react';
import { Button } from '@primer/react';
import { DownloadIcon } from '@primer/octicons-react';
import * as XLSX from 'xlsx';

interface ExportExcelProps {
    data: any[];
    columns: any[];
    filename?: string;
    variant?: "default" | "primary" | "danger" | "invisible" | "outline";
    size?: "small" | "medium" | "large";
    disabled?: boolean;
    buttonText?: string;
    onExportStart?: () => void;
    onExportComplete?: () => void;
    onExportError?: (error: Error) => void;
}

const ExportExcel: React.FC<ExportExcelProps> = ({
    data,
    columns,
    filename = "DataExport.xlsx",
    variant = "default",
    size = "small",
    disabled = false,
    buttonText = "Export",
    onExportStart,
    onExportComplete,
    onExportError
}) => {

    // Hàm đệ quy để flatten nested columns
    const flattenColumns = (cols: any[]): any[] => {
        const result: any[] = [];
        
        cols.forEach(col => {
            if (col.columns && Array.isArray(col.columns)) {
                // Nếu có nested columns, đệ quy vào trong
                result.push(...flattenColumns(col.columns));
            } else {
                // Nếu không có nested columns, thêm vào result
                if (col.caption !== "selection" && col.caption !== "#") {
                    result.push(col);
                }
            }
        });
        
        return result;
    };

    // Hàm extract text từ React elements
    const extractTextFromElement = (element: any): string => {
        if (typeof element === "string" || typeof element === "number") {
            return String(element);
        }
        
        if (typeof element === "boolean") {
            return element ? "true" : "false";
        }

        if (React.isValidElement(element)) {
            const reactElement = element as React.ReactElement;
            
            // Xử lý các HTML elements
            if (typeof reactElement.type === 'string') {
                // Xử lý <a> tags
                if (reactElement.type === 'a' && reactElement.props.children) {
                    return extractTextFromElement(reactElement.props.children);
                }
                
                // Xử lý <span>, <div>, <p> tags
                if (['span', 'div', 'p'].includes(reactElement.type) && reactElement.props.children) {
                    return extractTextFromElement(reactElement.props.children);
                }
                
                // Xử lý <i> tags (icons) - return empty string
                if (reactElement.type === 'i') {
                    return "";
                }
            }
            
            // Xử lý React components
            if (reactElement.props) {
                // Xử lý Checkbox component
                if (typeof reactElement.props.checked === "boolean") {
                    return reactElement.props.checked ? "✓" : "✗";
                }
                
                // Xử lý TextWithEllipse component
                if (reactElement.props.text !== undefined) {
                    return String(reactElement.props.text);
                }
                
                // Xử lý children
                if (reactElement.props.children !== undefined) {
                    return extractTextFromElement(reactElement.props.children);
                }
                
                // Xử lý value prop
                if (reactElement.props.value !== undefined) {
                    return String(reactElement.props.value);
                }
            }
        }

        if (Array.isArray(element)) {
            return element
                .map((child: any) => extractTextFromElement(child))
                .filter(text => text.trim() !== "")
                .join(" ");
        }

        // Xử lý React Fragment hoặc objects khác
        if (element && typeof element === "object") {
            if (element.props && element.props.children) {
                return extractTextFromElement(element.props.children);
            }
            
            // Xử lý các props có thể chứa text
            if (element.props && element.props.text) {
                return String(element.props.text);
            }
            
            if (element.props && element.props.value) {
                return String(element.props.value);
            }
        }

        return "";
    };

    // Hàm lấy giá trị cell để export
    const getCellValue = (col: any, row: any): string | number | boolean => {
        // Xử lý các trường hợp đặc biệt dựa trên column id
        switch (col.id) {
            case "ma_hs_ho_ten":
                return `${row.ma_hs || ""} ${row.ho_ten || ""}`.trim();
                
            case "trang_thai":
                return row.trang_thai || "";
                
            case "receive_user":
                const emails = row.receive_user?.split("##") || [];
                return emails.filter((email: string) => email.trim() !== "").join(", ");
                
            case "ngay_tao":
                if (row.ngay_tao) {
                    try {
                        const date = new Date(row.ngay_tao);
                        const formatted = date.toLocaleDateString("vi-VN");
                        return formatted === "1/1/1" || formatted === "Invalid Date" ? "" : formatted;
                    } catch {
                        return "";
                    }
                }
                return "";
                
            case "send_time":
                if (row.send_time) {
                    try {
                        const date = new Date(row.send_time);
                        return date.toLocaleString("vi-VN");
                    } catch {
                        return "";
                    }
                }
                return "";
                
            case "is_tao_chung_tu":
            case "is_da_ghi_nhan_tien":
                return row[col.id] === true ? "✓" : "✗";
                
            case "pdf_file":
                return row.pdf_file ? "Có file" : "Không có file";
                
            case "noi_dung":
                return row.noi_dung || "";
                
            case "nguoi_tao_chung_tu":
                return row.nguoi_tao_chung_tu || "";
                
            case "send_user":
                return row.send_user || "";
                
            case "note":
                return row.note || "";
                
            case "ngay_tao_file":
                return row.ngay_tao_file || "";
                
            case "ma_hs":
                return row.ma_hs || "";
        }
        
        // Nếu có cellRender, thử extract text
        if (col.cellRender) {
            const rendered = col.cellRender(row);
            
            if (typeof rendered === "string" || typeof rendered === "number" || typeof rendered === "boolean") {
                return rendered;
            }
            
            // Sử dụng hàm đệ quy để extract text từ các element phức tạp
            const extractedText = extractTextFromElement(rendered);
            if (extractedText.trim() !== "") {
                return extractedText.trim();
            }
        }

        // Fallback to raw data
        return row[col.dataField || col.id] ?? "";
    };

    const handleExport = () => {
        try {
            onExportStart?.();
            
            // Flatten tất cả nested columns
            const flatColumns = flattenColumns(columns);
            
            // Lọc ra các columns có thể export
            const visibleColumns = flatColumns.filter(
                (col) => col.caption !== "selection" && col.caption !== "#" && col.id
            );
            
            const headers = visibleColumns.map((col) => col.caption || col.id);
            const exportData = data.map((row) =>
                visibleColumns.map((col) => getCellValue(col, row))
            );
            
            const worksheet = XLSX.utils.aoa_to_sheet([headers, ...exportData]);
            const columnWidths = headers.map((header, i) => ({
                wch: Math.max(
                    header.length,
                    ...exportData.map((row) => String(row[i] ?? "").length)
                ) + 2,
            }));
            worksheet["!cols"] = columnWidths;
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

            // Xuất file Excel
            XLSX.writeFile(workbook, filename);
            
            onExportComplete?.();
        } catch (error) {
            onExportError?.(error as Error);
        }
    };

    return (
        <Button 
            size={size} 
            leadingVisual={DownloadIcon} 
            onClick={handleExport}
            disabled={disabled || !data || data.length === 0}
        >
            {buttonText}
        </Button>
    );
};

export default ExportExcel;
