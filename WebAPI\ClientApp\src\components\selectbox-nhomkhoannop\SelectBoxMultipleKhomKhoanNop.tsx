
import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { loadNhomKhoanNopStart } from '../../state/actions/nhomKhoanNopActions';
import { RootState } from '../../state/reducers';

type SelectBoxMultipleKhomKhoanNopProps = {
    isReadonly?: boolean;
    value?: number[];
    onValueChanged: (ids: number[]) => void;
    className?: string;
    preText?: string;
    width?: string | number;
    stylingMode?: 'outlined' | 'filled' | 'underlined';
    isShowClearBtn?: boolean;
    maxWidth?: any;
    size?: "small" | "medium" | "large";
};

const SelectBoxMultipleKhomKhoanNop = (props: SelectBoxMultipleKhomKhoanNopProps) => {
    const [filter, setFilter] = useState('');
    const [open, setOpen] = useState(false);
    const { dm_coso_id } = useSelector((state: RootState) => state.common);
    const { status, sf_nhomkhoannops } = useSelector((state: RootState) => state.nhomKhoanNop);
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(loadNhomKhoanNopStart());
    }, []);

    const dataSource = useMemo(() => {
        let data = sf_nhomkhoannops.filter(x => x.dm_coso_id === dm_coso_id);
        return data.map(x => ({ id: x.id, text: `${x.ma_nhom} - ${x.nhom_khoan_nop}` }));
    }, [sf_nhomkhoannops, dm_coso_id]);

    const removeVietnameseTones = (str: string) => {
        return str
            .normalize('NFD')
            .replace(/[̀-ͯ]/g, '')
            .replace(/đ/g, 'd')
            .replace(/Đ/g, 'D');
    };

    const filteredData = useMemo(() => {
        const normalizedFilter = removeVietnameseTones(filter.toLowerCase());
        return dataSource.filter(item => removeVietnameseTones(item.text.toLowerCase()).includes(normalizedFilter));
    }, [dataSource, filter]);

    const _selectedData = useMemo(() => {
        return dataSource.filter(item => props.value?.includes(item.id));
    }, [props.value, dataSource]);

    return (
        <SelectPanel
            renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                <Button
                    sx={{ width: '100%', display: 'flex', justifyContent: 'space-between', fontSize: props.size === "small" ? "12px" : "14px" }}
                    size={props.size}
                    trailingAction={TriangleDownIcon}
                    aria-labelledby={ariaLabelledBy}
                    {...anchorProps}
                >
                    <p style={{ maxWidth: props.maxWidth, overflow: 'hidden', textOverflow: 'ellipsis', fontSize: props.size === "small" ? "12px" : "14px" }}>
                        {children || 'Chọn nhóm khoản nộp'}
                    </p>
                </Button>
            )}

            title={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ flex: 1 }}>Chọn nhóm khoản nộp</Box>
                    {props.isShowClearBtn && (props.value?.length ?? 0) > 0 && (
                        <Button
                            trailingVisual={XCircleFillIcon}
                            variant='invisible'
                            sx={{ color: 'danger.emphasis' }}
                            onClick={() => props.onValueChanged([])}
                        >
                            Bỏ chọn
                        </Button>
                    )}
                </Box>
            }
            placeholderText='Search'
            open={open}
            onOpenChange={setOpen}

            items={filteredData}
            selected={_selectedData}
            onSelectedChange={(selectedItems: any[]) => {
                const newValues = selectedItems.map(item => item.id);
                props.onValueChanged(newValues);
            }}
            onFilterChange={setFilter}
            showItemDividers={true}
            overlayProps={{ width: 'large', height: 'medium' }}
        />
    );
};

export default SelectBoxMultipleKhomKhoanNop;
