import { ActionList, ActionMenu, Select } from '@primer/react';
import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { dotGuiTBTPApi } from "../../api/dotGuiTBTPApi";
import { useCommonContext } from "../../contexts/common";
import { sf_dotgui_tbtp } from "../../models/response/dot-gui/sf_dotgui_tbtp";
import { RootState } from "../../state/reducers";

type ICmbDotGuiProps = {
    isReadonly?: boolean,
    value?: number,
    width?: number,
    onValueChanged: (id: number, data?: sf_dotgui_tbtp) => void,
    className?: string,
    isShowClearButton?: boolean,
    preText?: string,
    stylingMode?: "outlined" | "filled" | "underlined",
    size?: "small" | "medium" | "large" 
}
const CmbDotGuiTBTP = (props: ICmbDotGuiProps) => {
    const [dotGuis, setDotGuis] = useState<sf_dotgui_tbtp[]>([]);
    const { nam_hoc, dm_coso_id } = useSelector((x: RootState) => x.common)
    useEffect(() => {
        handleReloadDotGuis();
    }, [dm_coso_id, nam_hoc])
    const handleReloadDotGuis = async () => {
        const res = await dotGuiTBTPApi.selectAll();
        if (res.is_success) {
            setDotGuis(res.data.filter((x: any) => x.nam_hoc === nam_hoc && x.dm_coso_id === dm_coso_id))
        }
    }
    const selectedData = useMemo(() => {
        if (props.value && dotGuis) {
            return dotGuis.find(x => x.id == props.value)
        }
        return undefined
    }, [dotGuis, props.value])
    const { translate } = useCommonContext();
    // return (
    //     <SelectBox dataSource={dotGuis}
    //         displayExpr={"ten_dot_gui"}
    //         valueExpr={"id"}
    //         value={props.value}
    //         stylingMode={props.stylingMode || "outlined"}
    //         readOnly={props.isReadonly === true}
    //         placeholder={translate("Chọn đợt gửi")}
    //         width={props.width ?? "100%"}
    //         className={"default_selectbox " + (props.className ? props.className : "")}
    //         showClearButton={false}
    //         onValueChanged={(e) => {
    //             if (e.event) {
    //                 const data = dotGuis.find(x => x.id === e.value)
    //                 props.onValueChanged(e.value, data)
    //             }
    //         }} />
    // );

    return (
        // <Select width={100} onChange={(e) => {
        //     const sf_dotgui_id: number = e.currentTarget.value ? parseInt(e.currentTarget.value) : 0
        //     props.onValueChanged(sf_dotgui_id, dotGuis.find(x => x.id == sf_dotgui_id))
        // }}
        //     value={`${props.value ?? 0}`}
        //     placeholder={translate("Chọn đợt gửi")}
        // >
        //     {dotGuis.map(x => {
        //         return (
        //             <Select.Option key={x.id} value={`${x.id}`}>{x.ten_dot_gui}</Select.Option>
        //         );
        //     })}
        // </Select>

        <div style={{ width: '100%' }}>
            <ActionMenu>
                <ActionMenu.Button
                    aria-label="Select school year"
                    size={props.size}
                    style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between'
                    }}
                >
                    {selectedData ? selectedData.ten_dot_gui : "Chọn đợt gửi"}
                </ActionMenu.Button>
                <ActionMenu.Overlay width="small">
                    <ActionList selectionVariant="single">
                        {props.isShowClearButton &&
                            <ActionList.Item key={0} selected={props.value != undefined && 0 === props.value}
                                onSelect={() => {
                                    props.onValueChanged(0)
                                }}
                            >
                                {"Chọn đợt gửi"}
                            </ActionList.Item>
                        }
                        {dotGuis && dotGuis.map((item, index) => {
                            return (
                                <ActionList.Item key={item.id} selected={props.value != undefined && item.id === props.value}
                                    onSelect={() => {
                                        props.onValueChanged(item.id, item)
                                    }}
                                >
                                    {item.ten_dot_gui}
                                </ActionList.Item>
                            );
                        })}
                    </ActionList>
                </ActionMenu.Overlay>
            </ActionMenu>
        </div>
    );
}
export default CmbDotGuiTBTP

