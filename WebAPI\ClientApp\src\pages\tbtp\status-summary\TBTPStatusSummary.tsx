import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Label, Box } from '@primer/react';
import { sf_tbtp_viewmodel } from '../../../models/response/tbtp/sf_tbtp';
import { actions } from '../../../state/actions/actionsWrapper';
import { RootState } from '../../../state/reducers';
import styles from "./TBTPStatusSummary.module.css"
interface ITBTPStatusSummaryProps {
    sf_tbtp_viewmodels: sf_tbtp_viewmodel[],
    hinhThucNopBomSelectedId: number,
    sendFilterSelectedId?: boolean,
    onHinhThucNopBomChanged: (id: number) => void,
    onSendFilterChanged: (value?: boolean) => void
}
const sendItems = [
    { id: 0, text: "Tất cả" },
    { id: 1, text: "<PERSON><PERSON> gửi", value: true },
    { id: 2, text: "<PERSON><PERSON><PERSON> gửi", value: false }
];
const TBTPStatusSummary = (props: ITBTPStatusSummaryProps) => {
    const { sf_hinhthucnopboms } = useSelector((x: RootState) => x.categorySource);
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(actions.categorySource.loadSfHinhThucNopBomStartAction())
    }, [sf_hinhthucnopboms.length == 0])
    const sendDataSource = useMemo(() => {
        return sendItems.map(x => {
            const count = props.sf_tbtp_viewmodels.filter(y => x.id === 0 || x?.value === y.is_sended).length;
            return {
                ...x,
                count
            }
        });
    }, [sendItems, props.sf_tbtp_viewmodels])
    return (
        <div className={styles.container}>
         
            <div className={styles.sendContainer}>
                <span className={styles.caption}>
                    <i className="fas fa-filter"></i>
                    Trạng thái
                </span>
                {sendDataSource.map(x => {
                    const isSelected = x.value === props.sendFilterSelectedId;
                    const getVariant = () => {
                        if (x.id === 0) return 'default'; // Tất cả
                        if (x.id === 1) return 'success'; // Đã gửi
                        if (x.id === 2) return 'attention'; // Chưa gửi
                        return 'default';
                    };

                    return (
                        <Box
                            key={x.id}
                            sx={{
                                display: 'inline-block',
                                margin: '0.25rem',
                                cursor: 'pointer',
                                opacity: isSelected ? 1 : 0.7,
                                transform: isSelected ? 'scale(1.05)' : 'scale(1)',
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                    opacity: 1,
                                    transform: 'scale(1.05)'
                                }
                            }}
                            onClick={() => {
                                props.onSendFilterChanged(x.value)
                            }}
                        >
                            <Label
                                variant={getVariant()}
                                size="large"
                                sx={{
                                    fontSize: '14px',
                                    fontWeight: isSelected ? 'bold' : 'normal',
                                    border: isSelected ? '2px solid' : '1px solid',
                                    borderColor: isSelected ? 'accent.emphasis' : 'border.default'
                                }}
                            >
                                {x.text} ({x.count})
                            </Label>
                        </Box>
                    );
                })}
            </div>
        </div>
    );
};

export default TBTPStatusSummary;