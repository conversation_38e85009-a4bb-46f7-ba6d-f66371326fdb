import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Label, Box } from '@primer/react';
import { sf_tbtp_viewmodel } from '../../../models/response/tbtp/sf_tbtp';
import { actions } from '../../../state/actions/actionsWrapper';
import { RootState } from '../../../state/reducers';
import styles from "./TBTPStatusSummary.module.css"
interface ITBTPStatusSummaryProps {
    sf_tbtp_viewmodels: sf_tbtp_viewmodel[],
    hinhThucNopBomSelectedId: number,
    sendFilterSelectedId?: boolean,
    onHinhThucNopBomChanged: (id: number) => void,
    onSendFilterChanged: (value?: boolean) => void
}
const sendItems = [
    { id: 0, text: "Tất cả" },
    { id: 1, text: "<PERSON><PERSON> gửi", value: true },
    { id: 2, text: "<PERSON><PERSON><PERSON> gửi", value: false }
];
const TBTPStatusSummary = (props: ITBTPStatusSummaryProps) => {
    const { sf_hinhthucnopboms } = useSelector((x: RootState) => x.categorySource);
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(actions.categorySource.loadSfHinhThucNopBomStartAction())
    }, [sf_hinhthucnopboms.length == 0])
    const sendDataSource = useMemo(() => {
        return sendItems.map(x => {
            const count = props.sf_tbtp_viewmodels.filter(y => x.id === 0 || x?.value === y.is_sended).length;
            return {
                ...x,
                count
            }
        });
    }, [sendItems, props.sf_tbtp_viewmodels])
    return (
        <div className={styles.container}>
         
            <div className={styles.sendContainer}>
                {sendDataSource.map(x => {
                    const isSelected = x.value === props.sendFilterSelectedId;
                    const getVariant = () => {
                        if (x.id === 0) return 'default'; // Tất cả
                        if (x.id === 1) return 'success'; // Đã gửi
                        if (x.id === 2) return 'attention'; // Chưa gửi
                        return 'default';
                    };

                    return (
                        <Box
                            key={x.id}
                            sx={{
                                display: 'inline-block',
                                margin: '0.125rem',
                                cursor: 'pointer',
                                opacity: isSelected ? 1 : 0.8,
                                '&:hover': {
                                    opacity: 1
                                }
                            }}
                            onClick={() => {
                                props.onSendFilterChanged(x.value)
                            }}
                        >
                            <Label
                                variant={getVariant()}
                                size="small"
                                sx={{
                                    fontSize: '12px',
                                    fontWeight: isSelected ? '700' : '600',
                                    border: '1px solid',
                                    borderColor: isSelected ? 'accent.emphasis' : 'border.default'
                                }}
                            >
                                {x.text} ({x.count})
                            </Label>
                        </Box>
                    );
                })}
            </div>
        </div>
    );
};

export default TBTPStatusSummary;