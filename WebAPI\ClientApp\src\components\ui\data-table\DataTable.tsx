import {
    DownloadIcon,
    SearchIcon,
    SortAscIcon,
    SortDescIcon,
} from "@primer/octicons-react";
import {
    ActionList,
    ActionMenu,
    Box,
    Button,
    Checkbox,
    Octicon,
    Pagination
} from "@primer/react";
import clsx from "clsx";
import React, { ReactNode, useEffect, useMemo, useState } from "react";
import { Column, useTable } from "react-table";
import { useDebounce } from "use-debounce";
import * as XLSX from "xlsx";
import { PlaceHolder } from "../place-holder";
import Text from "../text";
import TextInput from "../text-input";
import styles from "./DataTable.module.css";
export enum eSortMode {
    ASC = "asc",
    DESC = "desc",
}
function removeAccents(str: string) {
    if (!str) return str;
    return str
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .replace(/đ/g, "d")
        .replace(/Đ/g, "D");
}
function dynamicSort(property: string, mode: eSortMode) {
    var sortOrder = 1;
    if (mode === eSortMode.ASC) {
        sortOrder = 1;
    } else {
        sortOrder = -1;
    }
    return function (a: any, b: any) {
        var result =
            a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;
        return result * sortOrder;
    };
}
export interface IFocusCell {
    rowData: any;
    dataField: string;
}
interface ISelectionProps {
    keyExpr?: string;
    mode: "multiple" | "single";
    onSelectionChanged: (keys: number[]) => void;
    selectedRowKeys?: number[];
}
interface ISortConfig {
    enable: boolean;
    field?: string;
    mode: eSortMode;
    onValueChanged?: (field: string, mode: eSortMode) => void;
}
interface IPaging {
    enable: boolean;
    pageSize?: number;
    pageSizeItems?: number[];
}

export interface IColumn {
    caption?: string;
    dataField?: string;
    isMainColumn?: boolean;
    fixed?: boolean;
    id?: string;
    cellRender?: (data: any) => JSX.Element;
    headerRender?: (data: any) => ReactNode;
    columns?: IColumn[];
    width?: number | string | undefined;
    minWidth?: number | string | undefined;
    maxWidth?: number | string | undefined;
    align?: "left" | "center" | "right";
    isAllowFocus?: boolean;
}
interface ISummaryItem {
    name: string;
    column: string;
    summaryType: "sum" | "custom";
    render: (value: number) => string | JSX.Element;
}

interface ISummary {
    calculateCustomSummary?: (column: string, data: any[]) => number;
    items: ISummaryItem[];
}
interface ICheckDontSaveChange {
    fields: string[];
    rootDataSource: any[];
}
export interface IRangeSelectionKeyDownData {
    keyCode: string,
    rowKeys: any,
    dataFields: string[]
}
interface IRangeSelectionProps {
    onKeyDown: (data: IRangeSelectionKeyDownData) => void
}
interface IFilterRowConfig {
    enable: boolean,
    onConditionChanged?: (cond: any) => void
}
interface IDataTableProps {
    width?: string;
    columns: IColumn[];
    data: any[];
    height?: string;
    title?: string;
    titleComponent?: React.ReactNode;
    subTitle?: string;
    subTitleComponent?: React.ReactNode;
    actionComponent?: React.ReactNode;
    isLoading?: boolean;
    paging?: IPaging;
    searchEnable?: boolean;
    sortConfig?: ISortConfig;
    exportEnable?: boolean;
    selection?: ISelectionProps;
    onFocuseCellChanged?: (data: IFocusCell) => void;
    onClick?: (data: IFocusCell) => void;

    focusedCell?: IFocusCell;
    checkDontSaveChange?: ICheckDontSaveChange;
    emptyComponent?: React.ReactNode;
    rangeSelection?: IRangeSelectionProps,
    isNoPaddingCell?: boolean,
    summary?: ISummary,
    filterRow?: IFilterRowConfig

}
const DataTable = (props: IDataTableProps) => {
    const [search_key, setSearch_key] = useState("");
    const [searchKeyDelayed] = useDebounce(search_key, 1000);

    const [pageIndex, setPageIndex] = useState(0);
    const [pageSize, setPageSize] = useState(props.paging?.pageSize ?? 50);

    const [sortConfig, setSortConfig] = useState(props.sortConfig);
    const [selectedCells, setSelectedCells] = useState(new Set());
    const [isSelecting, setIsSelecting] = useState(false);
    const [startCell, setStartCell] = useState<any>(null);
    const [conditions, setConditions] = useState<any[]>([
        // { field: "", operator: "equals", value: "" },
    ]);
    const [logicOperator, setLogicOperator] = useState("and");

    // Bắt đầu chọn vùng
    const handleMouseDown = (rowIndex: number, colIndex: number) => {
        setIsSelecting(true);
        setStartCell({ rowIndex, colIndex });
        setSelectedCells(new Set([`${rowIndex}-${colIndex}`]));
    };
    // Kéo chuột để chọn vùng
    const handleMouseMove = (rowIndex: number, colIndex: number) => {
        if (isSelecting && startCell) {
            const { rowIndex: startRow, colIndex: startCol } = startCell;

            const minRow = Math.min(startRow, rowIndex);
            const maxRow = Math.max(startRow, rowIndex);
            const minCol = Math.min(startCol, colIndex);
            const maxCol = Math.max(startCol, colIndex);

            const newSelectedCells = new Set();
            for (let r = minRow; r <= maxRow; r++) {
                for (let c = minCol; c <= maxCol; c++) {
                    newSelectedCells.add(`${r}-${c}`);
                }
            }
            setSelectedCells(newSelectedCells);
        }
    };
    // Kết thúc chọn vùng
    const handleMouseUp = () => {
        setIsSelecting(false);
        setStartCell(null);
    };
    const {
        data,
        title,
        titleComponent,
        subTitle,
        subTitleComponent,
        actionComponent,
        isLoading,
        paging,
        height,
        searchEnable,

        exportEnable,
    } = props;
    const onFilterChange = (field: any, operator?: any, value?: any) => {

        if (!value) {
            removeCondition(field);
            return;
        }
        const cond = conditions.find(x => x.field === field);

        if (cond) {
            updateCondition(field, operator, value)
        } else {
            addCondition(field, operator, value)
        }
    }
    // Thêm điều kiện mới
    const addCondition = (field: any, operator?: any, value?: any) => {
        setConditions([...conditions.filter(x => x.field && x.field !== field), { field: field, operator: operator ?? "equals", value: value ?? "" }]);
    };
    // Xóa điều kiện
    const removeCondition = (field: any) => {
        const newConditions = conditions.filter(x => x.field !== field);
        setConditions(newConditions);
        applyFilter(newConditions);
    };
    // Cập nhật điều kiện
    const updateCondition = (field: any, operator?: any, value?: any) => {

        setConditions(conditions.map(c => {
            if (c.field === field) {
                return {
                    ...c,
                    operator: operator ?? "equals",
                    value
                }
            }
            return c;
        }));
        // applyFilter(newConditions);
    };
    // Áp dụng bộ lọc
    const applyFilter = (conds: any) => {
        let result: any[] = [];
        const filterExpression = conds.map((cond: any) => [cond.field, cond.operator, cond.value])
            .reduce((acc: any, curr: any, i: any) => (i > 0 ? [...acc, logicOperator, curr] : [curr]), []);
        // console.log({
        //     filterExpression: JSON.stringify(filterExpression),
        //     conds
        // });

        if (!filterExpression || filterExpression.length === 0) {
            result = pagedData;
        } else {
            result = pagedData.filter((item) => {
                let result = true;
                let currentOperator = "and";

                for (let i = 0; i < filterExpression.length; i++) {
                    const expr = filterExpression[i];
                    // debugger
                    if (typeof expr === "string") {
                        currentOperator = expr;
                        continue;
                    }

                    const [field, operator, value] = expr;
                    const itemValue = item[field];

                    let conditionResult;
                    switch (operator) {
                        case "equals":
                            conditionResult = itemValue === value;
                            break;
                        case "contains":
                            conditionResult = String(itemValue).toLowerCase().includes(String(value).toLowerCase());
                            break;
                        case "greaterThan":
                            conditionResult = Number(itemValue) > Number(value);
                            break;
                        case "lessThan":
                            conditionResult = Number(itemValue) < Number(value);
                            break;
                        case "greaterThanOrEqual":
                            conditionResult = Number(itemValue) >= Number(value);
                            break;
                        case "lessThanOrEqual":
                            conditionResult = Number(itemValue) <= Number(value);
                            break;
                        default:
                            conditionResult = true;
                    }

                    result = currentOperator === "and" ? result && conditionResult : result || conditionResult;
                    // debugger
                }
                // debugger
                return result;
            });
        }



    };
    useEffect(() => {
        applyFilter(conditions)
    }, [conditions])
    const sort_by = useMemo(() => {
        return sortConfig?.field ?? "";
    }, [sortConfig]);
    useEffect(() => {
        if (props.paging) {
            setPageSize(props.paging.pageSize ?? 50);
        }
    }, [props.paging]);
    useEffect(() => {
        setPageIndex(0);
    }, [sort_by]);
    useEffect(() => {
        const handleKeyDown = (e: any) => {
            if (selectedCells.size > 0 && rows.length > 0) {
                let rowIndexs: number[] = [];
                let colIndexs: number[] = [];
                selectedCells.forEach((cell: any) => {
                    const [rowIndx, colIndex] = cell.split('-').map((c: any) => parseInt(c));
                    if (!rowIndexs.includes(rowIndx)) rowIndexs.push(rowIndx);
                    if (!colIndexs.includes(colIndex)) colIndexs.push(colIndex);
                })
                rowIndexs = Array.from(new Set(rowIndexs));
                colIndexs = Array.from(new Set(colIndexs));
                let rowKeys: number[] = [];
                let dataFields: string[] = [];
                rowIndexs.forEach(rowIndex => {
                    const rowData = rows[rowIndex].original;
                    if (rowData) rowKeys.push(rowData.id)
                })
                colIndexs.forEach(colIndex => {
                    const cell = rows[0].cells[colIndex];
                    if (cell) {
                        const column: any = cell.column;
                        dataFields.push(column?.dataField ?? "")
                    }
                })


                if (props.rangeSelection && props.rangeSelection.onKeyDown) {
                    props.rangeSelection.onKeyDown({
                        dataFields,
                        rowKeys,
                        keyCode: e.key
                    })
                }

            }
        };

        document.addEventListener("keydown", handleKeyDown);

        // Dọn dẹp sự kiện khi component unmount
        return () => {
            document.removeEventListener("keydown", handleKeyDown);
        };
    }, [selectedCells]);
    const sort_by_name = useMemo(() => {
        return props.columns
            .filter((x) => x.caption === sort_by)
            .map((x) => x.caption)
            .join(",");
    }, [props.columns, sort_by]);
    const filterdData: any[] = useMemo(() => {
        const fieledColumns = props.columns.filter(col => col.dataField);
        const temp = data.map(x => {
            let isIncluded: boolean = false;
            fieledColumns.forEach(col => {
                if (!isIncluded) {
                    if (x[`${col.dataField}`])
                        if (removeAccents(x[`${col.dataField}`].toString().toLowerCase())
                            .includes(removeAccents(searchKeyDelayed.toLowerCase()))
                        ) {
                            isIncluded = true;
                        }
                }
            })
            if (isIncluded) {
                return x;
            }
            return undefined;
        }).filter(x => x !== undefined)

        const tempSort = temp.sort(dynamicSort(sort_by, sortConfig?.mode ?? eSortMode.DESC));
        let resultData: any[] = tempSort;
        const filterExpression = conditions.map((cond: any) => [cond.field, cond.operator, cond.value])
            .reduce((acc: any, curr: any, i: any) => (i > 0 ? [...acc, logicOperator, curr] : [curr]), []);

        if (!filterExpression || filterExpression.length === 0) {
            resultData = tempSort;
        } else {
            resultData = tempSort.filter((item) => {
                let result = true;
                let currentOperator = "and";

                for (let i = 0; i < filterExpression.length; i++) {
                    const expr = filterExpression[i];
                    // debugger
                    if (typeof expr === "string") {
                        currentOperator = expr;
                        continue;
                    }

                    const [field, operator, value] = expr;
                    const itemValue = item[field];

                    let conditionResult;
                    switch (operator) {
                        case "equals":
                            conditionResult = itemValue === value;
                            break;
                        case "contains":
                            conditionResult = String(itemValue).toLowerCase().includes(String(value).toLowerCase());
                            break;
                        case "greaterThan":
                            conditionResult = Number(itemValue) > Number(value);
                            break;
                        case "lessThan":
                            conditionResult = Number(itemValue) < Number(value);
                            break;
                        case "greaterThanOrEqual":
                            conditionResult = Number(itemValue) >= Number(value);
                            break;
                        case "lessThanOrEqual":
                            conditionResult = Number(itemValue) <= Number(value);
                            break;
                        default:
                            conditionResult = true;
                    }

                    result = currentOperator === "and" ? result && conditionResult : result || conditionResult;
                    // debugger
                }
                // debugger
                return result;
            });
        }


        return resultData;
    }, [searchKeyDelayed, data, props.columns, sort_by, sortConfig?.mode, conditions])
    const pageCount = useMemo(() => {
        if (pageSize <= 0) return filterdData.length;
        const c = Math.floor(filterdData.length / pageSize);
        if (c * pageSize < filterdData.length) return c + 1;
        return c;
    }, [filterdData.length, pageSize]);

    const pagedData = useMemo(() => {
        if (paging?.enable === true) {
            const data = filterdData.slice(
                pageSize * pageIndex,
                pageSize * pageIndex + pageSize
            );
            return data;
        }
        return filterdData;
    }, [filterdData, pageIndex, pageSize]);
    const summaryData = useMemo(() => {
        if (!props.summary || !props.data || !props.summary.items) return {};
        const totals: { [key: string]: number } = {};
        props.summary.items.forEach((item) => {
            if (item.summaryType === "sum") {
                const sum = props.data.reduce((sum, row) => sum + (row[item.column] || 0), 0);
                totals[item.column] = sum;
            } else if (item.summaryType === "custom" && props.summary?.calculateCustomSummary) {
                totals[item.column] = props.summary.calculateCustomSummary(item.column, props.data);
            }
        });
        return totals;
    }, [props.summary, props.data]);
    const settingColum = (column: IColumn): Column => {
        const col: any = {
            ...column,
            width: column.width,
            minWidth: column.minWidth,
            columns:
                column.columns && column.columns.length > 0
                    ? column.columns.map((x) => settingColum(x))
                    : undefined,
            Header: column.caption ?? "",
            accessor: column.dataField ?? column.id ?? "",
        };
        return col;
    };
    const extractTextFromElement = (element: any): string => {
        if (typeof element === "string" || typeof element === "number") {
            return String(element);
        }

        if (typeof element === "boolean") {
            return element ? "true" : "false";
        }

        if (React.isValidElement(element)) {
            const reactElement = element as React.ReactElement;

            // Xử lý các HTML elements
            if (typeof reactElement.type === 'string') {
                // Xử lý <a> tags
                if (reactElement.type === 'a' && reactElement.props.children) {
                    return extractTextFromElement(reactElement.props.children);
                }

                // Xử lý <span>, <div>, <p> tags
                if (['span', 'div', 'p'].includes(reactElement.type) && reactElement.props.children) {
                    return extractTextFromElement(reactElement.props.children);
                }

                // Xử lý <i> tags (icons) - return empty string
                if (reactElement.type === 'i') {
                    return "";
                }
            }

            // Xử lý React components
            if (reactElement.props) {
                // Xử lý Checkbox component
                if (typeof reactElement.props.checked === "boolean") {
                    return reactElement.props.checked ? "✓" : "✗";
                }

                // Xử lý TextWithEllipse component
                if (reactElement.props.text !== undefined) {
                    return String(reactElement.props.text);
                }

                // Xử lý children
                if (reactElement.props.children !== undefined) {
                    return extractTextFromElement(reactElement.props.children);
                }

                // Xử lý value prop
                if (reactElement.props.value !== undefined) {
                    return String(reactElement.props.value);
                }
            }
        }

        if (Array.isArray(element)) {
            return element
                .map((child: any) => extractTextFromElement(child))
                .filter(text => text.trim() !== "")
                .join(" ");
        }

        // Xử lý React Fragment hoặc objects khác
        if (element && typeof element === "object") {
            if (element.props && element.props.children) {
                return extractTextFromElement(element.props.children);
            }

            // Xử lý các props có thể chứa text
            if (element.props && element.props.text) {
                return String(element.props.text);
            }

            if (element.props && element.props.value) {
                return String(element.props.value);
            }
        }

        return "";
    };

    const getCellValue = (col: any, row: any): string | number | boolean => {
        // Xử lý các trường hợp đặc biệt dựa trên column id
        switch (col.id) {
            case "ma_hs_ho_ten":
                return `${row.ma_hs || ""} ${row.ho_ten || ""}`.trim();

            case "trang_thai":
                return row.trang_thai || "";

            case "receive_user":
                const emails = row.receive_user?.split("##") || [];
                return emails.filter((email: string) => email.trim() !== "").join(", ");

            case "ngay_tao":
                if (row.ngay_tao) {
                    try {
                        const date = new Date(row.ngay_tao);
                        const formatted = date.toLocaleDateString("vi-VN");
                        return formatted === "1/1/1" || formatted === "Invalid Date" ? "" : formatted;
                    } catch {
                        return "";
                    }
                }
                return "";

            case "send_time":
                if (row.send_time) {
                    try {
                        const date = new Date(row.send_time);
                        return date.toLocaleString("vi-VN");
                    } catch {
                        return "";
                    }
                }
                return "";

            case "is_tao_chung_tu":
            case "is_da_ghi_nhan_tien":
                return row[col.id] === true ? "✓" : "✗";

            case "pdf_file":
                return row.pdf_file ? "Có file" : "Không có file";

            case "noi_dung":
                return row.noi_dung || "";

            case "nguoi_tao_chung_tu":
                return row.nguoi_tao_chung_tu || "";

            case "send_user":
                return row.send_user || "";

            case "note":
                return row.note || "";

            case "ngay_tao_file":
                return row.ngay_tao_file || "";

            case "ma_hs":
                return row.ma_hs || "";
        }

        // Nếu có cellRender, thử extract text
        if (col.cellRender) {
            const rendered = col.cellRender(row);

            if (typeof rendered === "string" || typeof rendered === "number" || typeof rendered === "boolean") {
                return rendered;
            }

            // Sử dụng hàm đệ quy để extract text từ các element phức tạp
            const extractedText = extractTextFromElement(rendered);
            if (extractedText.trim() !== "") {
                return extractedText.trim();
            }
        }

        // Fallback to raw data
        return row[col.dataField || col.id] ?? "";
    };

    // Hàm đệ quy để flatten nested columns
    const flattenColumns = (cols: any[]): any[] => {
        const result: any[] = [];

        cols.forEach(col => {
            if (col.columns && Array.isArray(col.columns)) {
                // Nếu có nested columns, đệ quy vào trong
                result.push(...flattenColumns(col.columns));
            } else {
                // Nếu không có nested columns, thêm vào result
                if (col.caption !== "selection" && col.caption !== "#") {
                    result.push(col);
                }
            }
        });

        return result;
    };

    const handleExportToExcel = () => {
        if (!props.exportEnable) return;

        // Flatten tất cả nested columns
        const flatColumns = flattenColumns(columns);

        // Lọc ra các columns có thể export
        const visibleColumns = flatColumns.filter(
            (col) => col.caption !== "selection" && col.caption !== "#" && col.id
        );

        console.log("Visible columns for export:", visibleColumns.map(c => ({ id: c.id, caption: c.caption })));

        const headers = visibleColumns.map((col) => col.caption || col.id);
        const exportData = props.data.map((row) =>
            visibleColumns.map((col) => {
                const value = getCellValue(col, row);
                console.log(`Column ${col.id}:`, value);
                return value;
            })
        );

        console.log("Export data sample:", exportData[0]);

        const worksheet = XLSX.utils.aoa_to_sheet([headers, ...exportData]);
        const columnWidths = headers.map((header, i) => ({
            wch: Math.max(
                header.length,
                ...exportData.map((row) => String(row[i] ?? "").length)
            ) + 2,
        }));
        worksheet["!cols"] = columnWidths;
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

        // Xuất file Excel
        XLSX.writeFile(workbook, "DataTableExport.xlsx");
    };

    const columns: any[] = useMemo(() => {
        let col = props.columns.map((x) => {
            return settingColum(x);
        });
        if (props.selection?.mode === "multiple") {
            const selectionCol: any = {
                id: "selection",
                width: "40px",
                align: "center",
                cellRender: (data: any) => {
                    const id = data.id;
                    return (
                        <Checkbox
                            checked={(props.selection?.selectedRowKeys ?? []).includes(id)}
                            disabled={data.disabled}
                            onChange={(e) => {
                                if (e.target.checked) {
                                    props.selection?.onSelectionChanged([
                                        ...(props.selection?.selectedRowKeys ?? []),
                                        id,
                                    ]);
                                } else {
                                    props.selection?.onSelectionChanged([
                                        ...(props.selection?.selectedRowKeys ?? []).filter(
                                            (x) => x !== id
                                        ),
                                    ]);
                                }
                            }}
                        />
                    );
                },
                headerRender: () => {
                    return (
                        <Checkbox
                            checked={
                                filterdData.filter((g) => !g.disabled).length > 0 &&
                                filterdData
                                    .filter((g) => !g.disabled)
                                    .find(
                                        (x) =>
                                            !(props.selection?.selectedRowKeys ?? []).includes(x.id)
                                    ) === undefined
                            }
                            onChange={(e) => {
                                const ids = filterdData
                                    .filter((g) => !g.disabled)
                                    ?.map((x) => x.id);
                                if (e.target.checked) {
                                    props.selection?.onSelectionChanged(
                                        Array.from(
                                            new Set([
                                                ...(props.selection?.selectedRowKeys ?? []),
                                                ...ids,
                                            ])
                                        )
                                    );
                                } else {
                                    props.selection?.onSelectionChanged(
                                        Array.from(
                                            new Set(
                                                (props.selection?.selectedRowKeys ?? []).filter(
                                                    (x) => !ids.includes(x)
                                                )
                                            )
                                        )
                                    );
                                }
                            }}
                        />
                    );
                },
            };
            return [selectionCol, ...col];
        }
        return col;
    }, [props.columns, props.selection?.mode, props.selection?.selectedRowKeys]);

    const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow, allColumns } =
        useTable<any>({ columns, data: pagedData });

    return (
        <Box>
            <Box id="toolbar" sx={{ display: "flex", mb: 1 }}>
                <Box
                    id="left"
                    sx={{
                        flex: 1,
                    }}
                >
                    {props.title && (
                        <Text
                            text={props.title}
                            sx={{
                                fontWeight: 600,
                                fontSize: 24,
                            }}
                        />
                    )}
                    {props.titleComponent && <Box>{titleComponent}</Box>}
                    {props.subTitle && <Box className={styles.subTitle}>{subTitle}</Box>}
                    {props.subTitleComponent && <Box>{subTitleComponent}</Box>}
                </Box>
                <Box
                    id="actions"
                    sx={{
                        display: "flex",
                        alignItems: "flex-start",
                    }}
                >
                    {actionComponent}
                    {exportEnable && (
                        <Box sx={{ ml: 2 }}>
                            {/* <IconButton
                                icon={DownloadIcon}
                                variant="default"
                                aria-label="Download"
                                onClick={handleExportToExcel}
                            /> */}

                            <Button variant="default" size='small' leadingVisual={DownloadIcon} onClick={handleExportToExcel}>Export</Button>

                        </Box>
                    )}
                    {searchEnable && (
                        <Box sx={{ ml: 2, mr: 2 }}>
                            <TextInput
                                leadingVisual={SearchIcon}
                                placeholder={"Search"}
                                value={search_key}
                                onChange={(e) => {
                                    setSearch_key(e.target.value);
                                }}
                            ></TextInput>
                        </Box>
                    )}
                    {sortConfig && sortConfig.enable && (
                        <Box sx={{ ml: 2 }}>
                            <ActionMenu>
                                <ActionMenu.Button
                                    leadingVisual={
                                        sortConfig?.mode === eSortMode.DESC
                                            ? SortDescIcon
                                            : SortAscIcon
                                    }
                                >
                                    Sort by <b>{sort_by_name}</b>
                                </ActionMenu.Button>
                                <ActionMenu.Overlay>
                                    <ActionList
                                        selectionVariant="single"
                                        role="menu"
                                        aria-label=""
                                    >
                                        {props.columns
                                            .filter((x) => x.dataField)
                                            .map((x) => {
                                                return (
                                                    <ActionList.Item
                                                        key={x.id}
                                                        role="menuitemcheckbox"
                                                        selected={x.dataField === sortConfig.field}
                                                        onSelect={() => {
                                                            setSortConfig({
                                                                ...sortConfig,
                                                                field: x.dataField ?? "",
                                                            });
                                                        }}
                                                    >
                                                        {x.caption}
                                                    </ActionList.Item>
                                                );
                                            })}

                                        <ActionList.Divider></ActionList.Divider>
                                        <ActionList.Item
                                            role="menuitemcheckbox"
                                            selected={sortConfig.mode === eSortMode.ASC}
                                            onSelect={() => {
                                                setSortConfig({
                                                    ...sortConfig,
                                                    mode: eSortMode.ASC,
                                                });
                                            }}
                                        >
                                            <ActionList.LeadingVisual>
                                                <SortAscIcon />
                                            </ActionList.LeadingVisual>
                                            Ascending
                                        </ActionList.Item>
                                        <ActionList.Item
                                            role="menuitemcheckbox"
                                            selected={sortConfig.mode === eSortMode.DESC}
                                            onSelect={() => {
                                                setSortConfig({
                                                    ...sortConfig,
                                                    mode: eSortMode.DESC,
                                                });
                                            }}
                                        >
                                            <ActionList.LeadingVisual>
                                                <SortDescIcon />
                                            </ActionList.LeadingVisual>
                                            Descending
                                        </ActionList.Item>
                                    </ActionList>
                                </ActionMenu.Overlay>
                            </ActionMenu>
                        </Box>
                    )}

                </Box>
            </Box>
            <Box
                id="warpper"
                sx={{
                    position: "relative",
                }}
            >
                <Box
                    className={clsx(styles.container)}
                    sx={{
                        overflow: "auto",
                        height: props.height ?? "auto",
                    }}

                >
                    <table
                        {...getTableProps()}
                        className={clsx(
                            styles.myTable,
                            props.isNoPaddingCell ? styles.noPadding : undefined,
                            props.rangeSelection ? styles.rangeSelection : undefined,
                            paging?.enable === true ? styles.hasPaging : ""
                        )}
                        onMouseUp={props.rangeSelection ? handleMouseUp : undefined}

                    >
                        <thead className={styles.header}>
                            {headerGroups.map((headerGroup: any, hIdx) => (
                                <tr {...headerGroup.getHeaderGroupProps()} key={hIdx}>
                                    {headerGroup.headers.map((column: any, idx: number) => {
                                        return (
                                            <th
                                                {...column.getHeaderProps()}
                                                style={{
                                                    textAlign: column.align,
                                                    width: column.placeholderOf
                                                        ? column.placeholderOf.width
                                                        : column.width,
                                                    minWidth: column.placeholderOf
                                                        ? column.placeholderOf.minWidth
                                                        : column.minWidth,
                                                }}
                                                key={idx}
                                            >
                                                {column.headerRender
                                                    ? column.headerRender()
                                                    : column.render("Header")}
                                            </th>
                                        );
                                    })}
                                </tr>
                            ))}
                            {(props.filterRow?.enable === true || !props.filterRow) &&
                                <tr role="row">
                                    {allColumns.map((colum: any) => {
                                        return (
                                            <th style={{
                                                position: "unset"
                                            }}>
                                                {colum.dataField &&
                                                    <Box sx={{ margin: "-0.5rem -0.75rem" }}>
                                                        <TextInput
                                                            // className="text-input-noborder"
                                                            block
                                                            leadingVisual={<Box sx={{ color: "#656d76" }}>
                                                                <Octicon icon={SearchIcon} sx={{ width: "12px", height: "12px" }} />
                                                            </Box>}
                                                            sx={{
                                                                backgroundColor: "#F5F8FA",
                                                                // outline:"none!important",
                                                                boxShadow: "none",
                                                                border: "0!important",
                                                                mt: "1px"
                                                            }}
                                                            onValueDelayChanged={(value) => {
                                                                onFilterChange(colum.dataField, "contains", value)
                                                            }} />
                                                    </Box>
                                                }
                                            </th>
                                        );
                                    })}
                                </tr>
                            }
                        </thead>
                        {isLoading && (
                            <tbody {...getTableBodyProps()}>
                                {headerGroups.map((headerGroup: any) => (
                                    <tr {...headerGroup.getHeaderGroupProps()}>
                                        {headerGroup.headers.map((column: any, idx: number) => {
                                            return (
                                                <td
                                                    {...column.getHeaderProps()}
                                                    style={{
                                                        width: column.placeholderOf
                                                            ? column.placeholderOf.width ??
                                                            column.placeholderOf.minWidth
                                                            : column.width ?? column.minWidth,
                                                        fontSize: '12px'
                                                    }}
                                                >
                                                    <Box sx={{ m: "-1rem", mb: "-1.8rem" }}>
                                                        <PlaceHolder line_number={1} />
                                                    </Box>
                                                </td>
                                            );
                                        })}
                                    </tr>
                                ))}
                                {headerGroups.map((headerGroup: any) => (
                                    <tr {...headerGroup.getHeaderGroupProps()}>
                                        {headerGroup.headers.map((column: any, idx: number) => {
                                            return (
                                                <td
                                                    {...column.getHeaderProps()}
                                                    style={{
                                                        width: column.placeholderOf
                                                            ? column.placeholderOf.width ??
                                                            column.placeholderOf.minWidth
                                                            : column.width ?? column.minWidth,
                                                        fontSize: '12px'
                                                    }}
                                                >
                                                    <Box sx={{ m: "-1rem", mb: "-1.8rem" }}>
                                                        <PlaceHolder line_number={1} />
                                                    </Box>
                                                </td>
                                            );
                                        })}
                                    </tr>
                                ))}
                                {headerGroups.map((headerGroup: any) => (
                                    <tr {...headerGroup.getHeaderGroupProps()}>
                                        {headerGroup.headers.map((column: any, idx: number) => {
                                            return (
                                                <td
                                                    {...column.getHeaderProps()}
                                                    style={{
                                                        width: column.placeholderOf
                                                            ? column.placeholderOf.width ??
                                                            column.placeholderOf.minWidth
                                                            : column.width ?? column.minWidth,
                                                        fontSize: '12px'
                                                    }}
                                                >
                                                    <Box sx={{ m: "-1rem", mb: "-1.8rem" }}>
                                                        <PlaceHolder line_number={1} />
                                                    </Box>
                                                </td>
                                            );
                                        })}
                                    </tr>
                                ))}
                            </tbody>
                        )}
                        {!isLoading && (
                            <>
                                {pagedData.length > 0 && (
                                    <tbody {...getTableBodyProps()}>
                                        {rows.map((row: any, rowIndex: number) => {
                                            prepareRow(row);
                                            let isSelectedSigle: boolean = false;
                                            if (props.selection) {
                                                if (props.selection.mode === "single") {
                                                    if (
                                                        props.selection.selectedRowKeys &&
                                                        props.selection.selectedRowKeys.length > 0 &&
                                                        props.selection.selectedRowKeys[0] ===
                                                        row.original.id
                                                    ) {
                                                        isSelectedSigle = true;
                                                    }
                                                }
                                            }
                                            return (
                                                <tr
                                                    {...row.getRowProps()}
                                                    onClick={() => {
                                                        if (
                                                            props.selection &&
                                                            props.selection.mode === "single"
                                                        ) {
                                                            props.selection.onSelectionChanged([
                                                                row.original.id,
                                                            ]);
                                                        }
                                                    }}
                                                >
                                                    {row.cells.map((cell: any, colIndex: number) => {
                                                        let isDontSave: boolean = false;
                                                        if (
                                                            props.checkDontSaveChange &&
                                                            cell.column.isAllowFocus
                                                        ) {
                                                            const rootRowData =
                                                                props.checkDontSaveChange.rootDataSource.find(
                                                                    (x) => x.id === row.original.id
                                                                );
                                                            if (rootRowData) {
                                                                const rootValue =
                                                                    rootRowData[cell.column.dataField];
                                                                const currentValue =
                                                                    row.original[cell.column.dataField];
                                                                if (rootValue !== currentValue) {
                                                                    isDontSave = true;
                                                                }
                                                            }
                                                        }
                                                        return (
                                                            <td
                                                                {...cell.getCellProps()}
                                                                style={{
                                                                    width: cell.column.width,
                                                                    minWidth: cell.column.minWidth || cell.column.width,
                                                                    fontWeight: cell.column.isMainColumn
                                                                        ? "600"
                                                                        : 400,
                                                                    textAlign: cell.column.align,
                                                                    backgroundColor: isSelectedSigle
                                                                        ? "#fff8c5"
                                                                        : undefined,
                                                                    fontSize: '12px'
                                                                }}
                                                                tabIndex={
                                                                    cell.column.isAllowFocus ? 0 : undefined
                                                                }
                                                                onFocus={() => {
                                                                    if (props.onFocuseCellChanged) {
                                                                        props.onFocuseCellChanged({
                                                                            rowData: row.original,
                                                                            dataField: cell.column.dataField,
                                                                        });
                                                                    }
                                                                }}
                                                                onClick={() => {
                                                                    if (props.onClick) {
                                                                        props.onClick({
                                                                            rowData: row.original,
                                                                            dataField: cell.column.dataField,
                                                                        });
                                                                    }
                                                                }}
                                                                className={clsx(
                                                                    props.focusedCell &&
                                                                        props.focusedCell.dataField ===
                                                                        cell.column.dataField &&
                                                                        props.focusedCell.rowData.id ===
                                                                        row.original.id
                                                                        ? styles.focused
                                                                        : "",
                                                                    isDontSave ? styles.isDoneSave : "",
                                                                    selectedCells.has(`${rowIndex}-${colIndex}`) ? styles.rangeSelected : ""
                                                                )}
                                                                onMouseDown={props.rangeSelection ? () => handleMouseDown(rowIndex, colIndex) : undefined}
                                                                onMouseMove={props.rangeSelection ? () => handleMouseMove(rowIndex, colIndex) : undefined}
                                                            >
                                                                {cell.column.cellRender
                                                                    ? cell.column.cellRender(row.original)
                                                                    : cell.render("Cell")}
                                                            </td>
                                                        );
                                                    })}
                                                </tr>
                                            );
                                        })}
                                    </tbody>

                                )}
                                {props.summary && props.summary.items && props.summary.items.length > 0 && (
                                    <tfoot className={styles.footer}>
                                        <tr>
                                            {headerGroups[0].headers.map((column: any, idx: number) => {
                                                if (column.id.includes("tong_tien") && column.columns) {
                                                    return column.columns.map((subColumn: any, subIdx: number) => {
                                                        const summaryItem = props.summary?.items.find(item => item.column === subColumn.dataField);
                                                        const value = summaryItem ? summaryData[summaryItem.column] || 0 : "";
                                                        return (
                                                            <td
                                                                key={`${idx}-${subIdx}`}
                                                                style={{
                                                                    width: '50px',
                                                                    minWidth: subColumn.minWidth,
                                                                    textAlign: subColumn.align || "right",
                                                                    fontWeight: "600",
                                                                }}
                                                            >
                                                                {summaryItem ? summaryItem.render(Number(value)) : ""}
                                                            </td>
                                                        );
                                                    });
                                                }
                                                return (
                                                    <td
                                                        key={idx}
                                                        style={{
                                                            width: '40px',
                                                            minWidth: column.minWidth,
                                                            textAlign: column.align || "left",
                                                            fontWeight: "600",
                                                        }}
                                                    >
                                                        {""}
                                                    </td>
                                                );
                                            })}
                                        </tr>
                                    </tfoot>
                                )}
                                {pagedData.length <= 0 && (
                                    <tbody>
                                        <tr>
                                            <td colSpan={props.columns.length + 1}>
                                                {props.emptyComponent}
                                            </td>
                                        </tr>
                                    </tbody>
                                )}

                            </>
                        )}
                    </table>
                </Box>
                {paging?.enable === true && pageCount > 0 && (
                    <Box
                        id="paging"
                        className={clsx(styles.pagingContainer)}
                        sx={{
                            display: "flex",
                            alignItems: "center",
                        }}
                    >
                        <Box sx={{ flex: 1 }}>
                            {props.paging?.pageSizeItems && (
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                    <Box
                                        sx={{
                                            fontSize: "11px",
                                            color: "fg.muted",
                                        }}
                                    >
                                        Page size&nbsp;&nbsp;
                                    </Box>
                                    {props.paging?.pageSizeItems.map((x) => {
                                        return (
                                            <Button
                                                key={x}
                                                className={clsx(
                                                    styles.pageSize,
                                                    x === pageSize ? styles.selected : ""
                                                )}
                                                variant="invisible"
                                                sx={{
                                                    color: "fg.default",
                                                    fontWeight: 400,
                                                }}
                                                size="small"
                                                onClick={() => {
                                                    setPageSize(x);
                                                    setPageIndex(0);
                                                }}
                                            >
                                                {x.toString()}
                                            </Button>
                                        );
                                    })}
                                </Box>
                            )}
                        </Box>
                        <Box id="info" sx={{ width: "100px", textAlign: "center" }}>
                            <Box
                                sx={{
                                    color: "fg.muted",
                                }}
                            >
                                {pageIndex * pageSize + 1} -{" "}
                                {(pageIndex + 1) * pageSize > data.length
                                    ? data.length
                                    : (pageIndex + 1) * pageSize}{" "}
                                of {data.length}
                            </Box>
                        </Box>
                        <Box
                            sx={{ flex: 1, display: "flex", flexDirection: "row-reverse" }}
                        >
                            {pageCount > 1 && (
                                <Pagination
                                    pageCount={pageCount}
                                    currentPage={pageIndex + 1}
                                    showPages={{
                                        narrow: false,
                                    }}
                                    onPageChange={(e, n) => {
                                        setPageIndex(n - 1);
                                    }}
                                />
                            )}
                        </Box>
                    </Box>
                )}
            </Box>
        </Box>
    );
};

export default DataTable;
