import {
    AlertIcon,
    FileBadgeIcon,
    SearchIcon,
    TrashIcon
} from "@primer/octicons-react";
import {
    ActionList,
    Box,
    Button,
    Checkbox,
    Dialog,
    Flash,
    FormControl,
    IconButton,
    Text,
    TextInput
} from "@primer/react";
import React, { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { hinhThucMienGiamApi } from "../../../../api/hinhThucMienGiamApi";
import { RootState } from "../../../../state/reducers";
import { showNotify } from "../../../../helpers/toast";

interface HinhThucMienGiamProps {
    ts_hocsinh_id: number,
}

interface MienGiamItem {
    id: number;
    ma_hinh_thuc: string;
    hinh_thuc_mien_giam: string;
    nhom_mien_giam: string;
    is_apdung_toantruong: boolean;
    dm_coso_id: number;
}

interface GroupedMienGiam {
    key: string;
    items: MienGiamItem[];
}

interface ApiResponse<T> {
    is_success: boolean;
    data?: T;
    message?: string;
}

const HinhThucMienGiam = (props: HinhThucMienGiamProps) => {
    const { nam_hoc, dm_coso_id } = useSelector((x: RootState) => x.common);
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [error, setError] = useState<string>("");

    const [listMienGiam, setListMienGiam] = useState<MienGiamItem[]>([]);
    const [selectedIds, setSelectedIds] = useState<number[]>([]);
    const [initialSelectedIds, setInitialSelectedIds] = useState<number[]>([]);
    const [isShowDeleteConfirm, setIsShowDeleteConfirm] = useState(false);
    const [itemToDelete, setItemToDelete] = useState<number | null>(null);


    useEffect(() => {
        getListMienGiamByHocSinh();
        getListMienGiam();
    }, [dm_coso_id, nam_hoc]);

    const getListMienGiam = async () => {
        try {
            const res: ApiResponse<MienGiamItem[]> =
                await hinhThucMienGiamApi.selectAllByNamHoc(
                    nam_hoc,
                    props.ts_hocsinh_id,
                    dm_coso_id
                );
            if (res.is_success && res.data) {
                setListMienGiam(res.data.filter(x => x.dm_coso_id === dm_coso_id));
            } else {
                setListMienGiam([]);
            }
        } catch (error) {
            console.error("Error fetching mien giam list:", error);
            setListMienGiam([]);
        }
    };

    const getListMienGiamByHocSinh = async () => {
        try {
            const res: ApiResponse<MienGiamItem[]> =
                await hinhThucMienGiamApi.selectByHocSinh(nam_hoc, props.ts_hocsinh_id, dm_coso_id);
            if (res.is_success && res.data) {
                const selectedMienGiamIds = res.data.map((item) => item.id);
                setSelectedIds(selectedMienGiamIds);
                setInitialSelectedIds(selectedMienGiamIds);
            }
        } catch (error) {
            console.error("Error fetching selected mien giam:", error);
        }
    };

    const handleCheckboxChange = async (
        itemId: number,
        nhomMienGiam: string,
        checked: boolean
    ) => {
        const itemsInSameGroup = listMienGiam
            .filter((item) => item.nhom_mien_giam === nhomMienGiam)
            .map((item) => item.id);

        setSelectedIds((prevSelectedIds) => {
            if (checked) {
                return prevSelectedIds
                    .filter((id) => !itemsInSameGroup.includes(id))
                    .concat(itemId);
            } else {
                return prevSelectedIds.filter((id) => id !== itemId);
            }
        });

        setError("");

        try {
            if (checked) {
                await hinhThucMienGiamApi.deleteDeXuatMienGiam({
                    id: props.ts_hocsinh_id,
                    nam_hoc: nam_hoc,
                    sf_hinhthucmiengiam_ids: itemsInSameGroup,
                });
                await hinhThucMienGiamApi.insertDeXuatMienGiam({
                    id: props.ts_hocsinh_id,
                    nam_hoc: nam_hoc,
                    sf_hinhthucmiengiam_ids: [itemId],
                });
                showNotify({ message: "Gán miễn giảm thành công", type: "success" })

            } else {
                await hinhThucMienGiamApi.deleteDeXuatMienGiam({
                    id: props.ts_hocsinh_id,
                    nam_hoc: nam_hoc,
                    sf_hinhthucmiengiam_ids: [itemId],
                });
                showNotify({ message: "Xóa miễn giảm thành công", type: "success" })

            }
        } catch (error) {
            console.error("Error updating selection:", error);
        }
    };

    const handleRemoveItem = (itemId: number) => {
        setItemToDelete(itemId);
        setIsShowDeleteConfirm(true);
    };
    const confirmDelete = async () => {
        if (itemToDelete !== null) {
            setSelectedIds((prevSelectedIds) =>
                prevSelectedIds.filter((id) => id !== itemToDelete)
            );

            try {
                await hinhThucMienGiamApi.deleteDeXuatMienGiam({
                    id: props.ts_hocsinh_id,
                    nam_hoc: nam_hoc,
                    sf_hinhthucmiengiam_ids: [itemToDelete],
                });
                showNotify({ message: "Xóa miễn giảm thành công", type: "success" })

            } catch (error) {
                console.error("Error deleting item:", error);
            } finally {
                setIsShowDeleteConfirm(false);
                setItemToDelete(null);
            }
        }
    };

    const groupedMienGiam = useMemo(() => {
        const filtered = listMienGiam.filter((item: MienGiamItem) => {
            const searchLower = searchQuery.toLowerCase();
            return (
                item.ma_hinh_thuc.toLowerCase().includes(searchLower) ||
                item.hinh_thuc_mien_giam.toLowerCase().includes(searchLower)
            );
        });

        return filtered.reduce(
            (groups: { [key: string]: GroupedMienGiam }, current) => {
                const key = current.nhom_mien_giam.toString();
                if (!groups[key]) {
                    groups[key] = {
                        key,
                        items: [],
                    };
                }
                groups[key].items.push(current);
                return groups;
            },
            {}
        );
    }, [listMienGiam, searchQuery]);

    const selectedItems = useMemo(() => {
        return listMienGiam.filter((item) => selectedIds.includes(item.id));
    }, [listMienGiam, selectedIds]);

    return (
        <Box sx={{ p: 1 }}>
            <Box sx={{ display: "flex", gap: 3, marginBottom: 1 }}>
                <FormControl sx={{ flex: 1 }}>
                    <Box sx={{ display: "flex" }}>
                        <TextInput
                            sx={{ flex: 1 }}
                            width={300}
                            leadingVisual={SearchIcon}
                            value={searchQuery}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                setSearchQuery(e.target.value)
                            }
                            placeholder="Nhập để tìm kiếm..."
                        />
                    </Box>
                </FormControl>
            </Box>
            {error && (
                <Flash variant="danger" sx={{ mb: 3 }}>
                    {error}
                </Flash>
            )}
            <Dialog
                isOpen={isShowDeleteConfirm}
                onDismiss={() => setIsShowDeleteConfirm(false)}
            >
                <Dialog.Header>
                    <Box display="flex" alignItems="center">
                        <div style={{ color: "orange" }}>
                            <AlertIcon size={28} className="" />
                        </div>

                        <Box ml={2}>Lưu ý</Box>
                    </Box>
                </Dialog.Header>{" "}
                <Box p={3}>
                    <Text>Bạn có chắc chắn muốn xóa mục miễn giảm này?</Text>
                    <Box display="flex" mt={3} justifyContent="flex-end">
                        <Box mr={2}>
                            <Button
                                variant="default"
                                onClick={() => setIsShowDeleteConfirm(false)}
                            >
                                Đóng
                            </Button>
                        </Box>
                        <Button variant="danger" onClick={confirmDelete}>
                            Xóa
                        </Button>
                    </Box>
                </Box>
            </Dialog>
            <Box sx={{ display: "flex", gap: 3 }}>
                <Box sx={{ flex: 1 }}>
                    <Box
                        sx={{
                            border: "1px solid",
                            borderColor: "border.default",
                            borderRadius: 2,
                            maxHeight: "calc(100vh - 390px)",
                            overflowY: "auto",
                        }}
                    >
                        {Object.values(groupedMienGiam).map((group) => (
                            <Box key={group.key}>
                                <Box
                                    sx={{
                                        bg: "canvas.subtle",
                                        p: 2,
                                        borderBottom: "1px solid",
                                        borderColor: "border.default",
                                        fontWeight: "bold",
                                        color: "fg.muted",
                                    }}
                                >
                                    Nhóm miễn giảm: {group.key}
                                </Box>
                                <ActionList>
                                    {group.items.map((item) => (
                                        <ActionList.Item
                                            key={item.id}
                                            style={
                                                selectedIds.includes(item.id)
                                                    ? { opacity: 0.5, pointerEvents: "none" }
                                                    : {}
                                            }
                                        >
                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    alignItems: "center",
                                                    width: "100%",
                                                }}
                                            >
                                                <Checkbox
                                                    checked={selectedIds.includes(item.id)}
                                                    onChange={(
                                                        e: React.ChangeEvent<HTMLInputElement>
                                                    ) => {
                                                        handleCheckboxChange(
                                                            item.id,
                                                            item.nhom_mien_giam,
                                                            e.target.checked
                                                        );
                                                    }}
                                                />
                                                <Box sx={{ display: "flex", flex: 1, ml: 2 }}>
                                                    <Box sx={{ width: "30%", pr: 2 }}>
                                                        {item.ma_hinh_thuc}
                                                    </Box>
                                                    <Box sx={{ width: "70%" }}>
                                                        {item.hinh_thuc_mien_giam}
                                                    </Box>
                                                </Box>
                                            </Box>
                                        </ActionList.Item>
                                    ))}
                                </ActionList>
                            </Box>
                        ))}
                    </Box>
                </Box>
                <Box sx={{ flex: 1 }} >
                    <Box
                        sx={{
                            border: "1px solid",
                            borderColor: "border.default",
                            borderRadius: 2,
                            maxHeight: "calc(100vh - 420px)",
                            overflowY: "auto",
                        }}
                    >
                        <Box
                            sx={{
                                bg: "canvas.subtle",
                                p: 2,
                                borderBottom: "1px solid",
                                borderColor: "border.default",
                                fontWeight: "bold",
                                color: "fg.muted",
                            }}
                        >
                            Danh sách hình thức miễn giảm áp dụng toàn trường
                        </Box>
                        <ActionList showDividers>
                            {selectedItems
                                .filter(item => item.is_apdung_toantruong)
                                .map((item) => (
                                    <ActionList.Item key={item.id}>
                                        <ActionList.LeadingVisual>
                                            <FileBadgeIcon size={16} />
                                        </ActionList.LeadingVisual>
                                        <Box sx={{ display: "flex", flex: 1, alignItems: "center" }}>
                                            <Box
                                                sx={{
                                                    width: "100%",
                                                    pr: 2,
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    whiteSpace: "nowrap",
                                                }}
                                            >
                                                {item.ma_hinh_thuc}:{" "}
                                                <span style={{ fontWeight: "400" }}>
                                                    {item.hinh_thuc_mien_giam}
                                                </span>
                                            </Box>
                                        </Box>
                                        <ActionList.Description variant="block">
                                            Nhóm miễn giảm: {item.nhom_mien_giam}
                                        </ActionList.Description>
                                    </ActionList.Item>
                                ))}
                        </ActionList>
                        <Box
                            sx={{
                                bg: "canvas.subtle",
                                p: 2,
                                borderBottom: "1px solid",
                                borderColor: "border.default",
                                fontWeight: "bold",
                                color: "fg.muted",
                                mt: 2 // Thêm margin top để tạo khoảng cách giữa 2 nhóm
                            }}
                        >
                            Danh sách hình thức miễn giảm
                        </Box>
                        <ActionList showDividers>
                            {selectedItems
                                .filter(item => !item.is_apdung_toantruong)
                                .map((item) => (
                                    <ActionList.Item key={item.id}>
                                        <ActionList.LeadingVisual>
                                            <FileBadgeIcon size={16} />
                                        </ActionList.LeadingVisual>
                                        <Box sx={{ display: "flex", flex: 1, alignItems: "center" }}>
                                            <Box
                                                sx={{
                                                    width: "100%",
                                                    pr: 2,
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    whiteSpace: "nowrap",
                                                }}
                                            >
                                                {item.ma_hinh_thuc}:{" "}
                                                <span style={{ fontWeight: "400" }}>
                                                    {item.hinh_thuc_mien_giam}
                                                </span>
                                            </Box>
                                        </Box>
                                        <ActionList.Description variant="block">
                                            Nhóm miễn giảm: {item.nhom_mien_giam}
                                        </ActionList.Description>
                                        <ActionList.TrailingVisual>
                                            <IconButton
                                                size="small"
                                                className="invisible"
                                                icon={TrashIcon}
                                                disabled={item.is_apdung_toantruong}
                                                variant="danger"
                                                aria-label="Delete"
                                                onClick={() => handleRemoveItem(item.id)}
                                            />
                                        </ActionList.TrailingVisual>
                                    </ActionList.Item>
                                ))}
                        </ActionList>
                    </Box>
                </Box>
            </Box>
        </Box>
    );
};

export default HinhThucMienGiam;
