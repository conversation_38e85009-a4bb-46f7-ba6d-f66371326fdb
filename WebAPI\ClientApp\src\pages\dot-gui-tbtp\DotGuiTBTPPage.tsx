import {
    PlusIcon,
    SyncIcon,
    TrashIcon
} from "@primer/octicons-react";
import { Box, Checkbox } from "@primer/react";
import moment from "moment";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { Transition } from 'semantic-ui-react';
import { dotGuiKhoanNopTbtpApi } from "../../api/dotGuiKhoanNopTbtpApi";
import { dotGuiTBTPApi, SF_DOTGUI_TBTP_LOCK_API } from "../../api/dotGuiTBTPApi";
import { hinhThucNopBomApi } from "../../api/hinhThucNopBom";
import { AnimationConfirm } from '../../components/confirm';
import Button from "../../components/ui/button";
import DataTable from "../../components/ui/data-table";
import Text from "../../components/ui/text";
import { useCommonContext } from "../../contexts/common";
import { showNotify } from "../../helpers/toast";
import { PageBaseStatus } from '../../models/pageBaseStatus';
import { sf_hinhthucnopbom } from "../../models/response/category/sf_hinhthucnopbom";
import { sf_dotgui_tbtp, sf_dotgui_tbtp_viewmodel } from '../../models/response/dot-gui/sf_dotgui_tbtp';
import { sf_dotgui_tbtp_khoannop_request } from "../../models/response/dot-gui/sf_dotgui_tbtp_khoannop";
import { changeDotGuiSelectionIds, closeDotGuiDeleteConfirm, closeDotGuiDetailModal, deleteDotGuiStart, showDotGuiDeleteConfirm, showDotGuiDetailModal } from '../../state/actions/dotGuiTBTPAction';
import { RootState } from '../../state/reducers';
import { EmailContentModal } from "../dot-gui/detail-modal/EmailContent";
import DanhSachKhoanNopModal from "./danh-sach-khoan-nop";
import DotGuiCreateLayout from "./DotGuiCreateLayout";


const DotGuiTBTPPage = () => {
    const [danhSachDotGuiSelectedIds, setDanhSachDotGuiSelectedIds] = useState<number[]>([]);

    const dispatch = useDispatch();
    const state = useSelector((x: RootState) => x.dotGuiTBTP)
    const [hinhThucNopBoms, setHinhThucNopBoms] = useState<sf_hinhthucnopbom[]>([]);
    const [isShowEmailContent, setIsShowEmailContent] = useState<boolean>(false);
    const [emailContent, setEmailContent] = useState<sf_dotgui_tbtp>();
    const { dm_coso_id, nam_hoc } = useSelector((x: RootState) => x.common)
    const [dataSource, setDataSource] = useState<sf_dotgui_tbtp[]>([]);
    const [khoanNopIds, setkhoanNopIds] = useState<number[]>([]);
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [isShowListKhoanNop, setIsShowListKhoanNop] = useState<boolean>(false);
    const [khoanNopSelectedIds, setKhoanNopSelectedIds] = useState<number[]>([]);
    const [hinhThucNopBomFilter, setHinhThucNopBomFilter] = useState<sf_hinhthucnopbom[]>([]);
    const { checkAccesiableTo } = useCommonContext();
    const [filterData, setFilterData] = useState<any>();

    const { status,
        sf_dotgui_tbtp_selected_ids,
        sf_dotgui_tbtp_viewmodels,
        sf_dotgui_tbtp_editing,
        is_show_detail_modal,
        is_show_delete_confirm,
        dm_truong_id,
        sf_dotgui_tbtp,
    } = state;
    useEffect(() => {
        handleReloadHinhThucNopBoms();
        handleReload();
    }, [status, dm_coso_id, nam_hoc])
    const grid = useRef<any>();
    const handleReloadHinhThucNopBoms = async () => {
        const res = await hinhThucNopBomApi.selectAll();
        if (res.is_success) {
            setHinhThucNopBoms(res.data.filter((x: any) => x.id > 0))
        }
    }
    useEffect(() => {
        if (sf_dotgui_tbtp_viewmodels) {
            setDataSource(sf_dotgui_tbtp_viewmodels.filter((x: any) => x.dm_coso_id === dm_coso_id && x.nam_hoc === nam_hoc))
        }
    }, [sf_dotgui_tbtp_viewmodels])

    useEffect(() => {
        if (state.sf_dotgui_tbtp && khoanNopIds.length > 0) {
            handleSaveChangesKhoanNop(state.sf_dotgui_tbtp.id, khoanNopIds)
        }
    }, [khoanNopIds, state, isSaving])
    // const handleReload = async () => {
    //     dispatch(loadDotGuiStart({
    //         dm_he_id: 0,
    //         dm_khoi_id: 0,
    //         dm_truong_id: dm_truong_id,
    //         nam_hoc: nam_hoc
    //     }));
    // }

    const isAccessibleLock = useMemo(() => {
        if (!checkAccesiableTo(SF_DOTGUI_TBTP_LOCK_API, 'POST')) return false;
        return true;
    }, [checkAccesiableTo]);
    const handleReload = async () => {
        const res = await dotGuiTBTPApi.selectAll();
        if (res.is_success) {
            setDataSource(res.data.filter((x: any) => x.dm_coso_id === dm_coso_id && x.nam_hoc === nam_hoc))
        }
    }
    const handleRefresh = () => {
        handleReload();
        onSelectionChanged([])
        setDanhSachDotGuiSelectedIds([]);
    }

    const handleCloseDeleteConfirm = useCallback(() => {
        dispatch(closeDotGuiDeleteConfirm())
    }, [])
    const handleShowDeleteConfirm = useCallback(() => {
        dispatch(showDotGuiDeleteConfirm())
    }, [])
    const handleInsertClick = useCallback(() => {
        dispatch(showDotGuiDetailModal());
    }, []);
    const handleBtnEditClicked = (data: sf_dotgui_tbtp_viewmodel) => {
        dispatch(showDotGuiDetailModal(data));
    }
    const handleExport = useCallback(() => {
        if (grid && grid.current && grid.current.instance) {
            grid.current.instance.exportToExcel();
        }
    }, [grid])
    const onSelectionChanged = useCallback((selectedRowKeys: number[]) => {
        dispatch(changeDotGuiSelectionIds(selectedRowKeys))
    }, [])
    const handleConfirmDelete = useCallback(() => {
        dispatch(deleteDotGuiStart(sf_dotgui_tbtp_selected_ids))
    }, [sf_dotgui_tbtp_selected_ids]);

    const handleCloseDetailModal = useCallback(() => {
        dispatch(closeDotGuiDetailModal())
    }, [])
    const handleSubmitDetailModal = async (data: sf_dotgui_tbtp, sf_khoannop_ids: number[]) => {
        setkhoanNopIds(sf_khoannop_ids)
        if (data.id === 0) {
            const res = await dotGuiTBTPApi.insert(data);
            if (res.is_success) {
                setIsSaving(true)
                await handleSaveChangesKhoanNop(res.data, sf_khoannop_ids)
            }
        }
        else {
            const res = await dotGuiTBTPApi.update(data);
            if (res.is_success) {
                setIsSaving(true)
                await handleSaveChangesKhoanNop(data.id, sf_khoannop_ids)
            }
        }
        showNotify({ message: "Cập nhật thành công", type: "success" })
        handleCloseDetailModal();
        setIsSaving(false)
        handleReload();

    }


    const handleSaveChangesKhoanNop = async (sf_dotgui_tbtp_id: number, sf_khoannop_ids: number[]) => {
        if (sf_dotgui_tbtp_id > 0) {
            const datasend: sf_dotgui_tbtp_khoannop_request = {
                sf_dotgui_tbtp_id: sf_dotgui_tbtp_id,
                sf_khoannop_ids: sf_khoannop_ids
            }
            const res = await dotGuiKhoanNopTbtpApi.update(datasend);
            if (!res.is_success) {
                showNotify({ message: "Cập nhật thất bại", type: "warning" })
            }
        }
        setIsSaving(false)
    }
    const handleReloadKhoanNopByDotGuis = async (sf_dotgui_tbtp_id: number) => {
        const res = await dotGuiKhoanNopTbtpApi.select_by_dotgui(sf_dotgui_tbtp_id);
        const sf_khoannop_ids = res.data.map((x: any) => x.sf_khoannop_id);
        setKhoanNopSelectedIds(sf_khoannop_ids)
    }
    const previewRender = (data: any) => {
        return (
            <div>
                <span
                    style={{ cursor: 'pointer', fontSize: '18px' }}
                    onClick={() => {
                        btnPreview_click(data);
                    }}
                >
                    <i aria-hidden="true" className="fa fa-cog" style={{ color: '#1c2845' }}></i>{' '}
                </span>
            </div>
        );
    };
    const previewKhoanNopRender = (data: any) => {
        return (
            <div>
                <span
                    style={{ cursor: 'pointer', fontSize: '18px' }}
                    onClick={() => {
                        btnPreviewKhoanNop_click(data);
                    }}
                >
                    <i aria-hidden="true" className="fa fa-cog" style={{ color: '#1c2845' }}></i>{' '}
                </span>
            </div>
        );
    };
    const hinhThucNopBomRender = (cell: any) => {
        const ids = cell.sf_hinhthucnopbom_ids.split(',');
        const names: string[] = [];
        ids.forEach((id: any) => {
            const hinhThucNopBom = hinhThucNopBoms.find(item => item.id.toString() === id.trim());
            if (hinhThucNopBom) {
                names.push(hinhThucNopBom.name);
            }
        });
        return (
            <div>
                {names.join(', ')}
            </div>
        );
    };
    const btnPreviewKhoanNop_click = (data: sf_dotgui_tbtp) => {
        if (data.sf_hinhthucnopbom_ids !== '') {
            const htnb_ids = data.sf_hinhthucnopbom_ids.split(',').map((x) => (parseInt(x) ?? 0));
            const htnb_filter = hinhThucNopBoms.filter((x: any) => htnb_ids.includes(x.id))
            setHinhThucNopBomFilter(htnb_filter)
        }
        handleReloadKhoanNopByDotGuis(data.id);
        setIsShowListKhoanNop(true);
    };
    const btnPreview_click = (data: sf_dotgui_tbtp) => {
        setEmailContent(data);
        setIsShowEmailContent(true);
    };
    return (<>
        <React.Fragment>
            <Box sx={{ p: 2, pt: 0 }}>
                <Transition animation="scale"
                    visible={true}
                    duration={100}
                    transitionOnMount={true}>
                    <Box>
                        <Box sx={{ m: 2 }}>
                            <DataTable
                                height="calc(100vh - 235px)"
                                titleComponent={<Text text="Danh sách đợt gửi TBTP" sx={{ fontSize: 16, fontWeight: 600 }} />}
                                data={dataSource}
                                emptyComponent={<Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        minHeight: "60vh",
                                        textAlign: "center"
                                    }}
                                >
                                    <img
                                        src="https://true-north-school.s3.cloudfly.vn/fee/datagrid_empty"
                                        alt="Không có dữ liệu"
                                        style={{ width: "60px", height: "60px", marginBottom: "5px" }}
                                    />
                                    <Box sx={{ fontSize: "13px", fontWeight: "400", color: "#8C8C8C" }}>
                                        Không có dữ liệu
                                    </Box>
                                </Box>
                                }
                                selection={{
                                    mode: "multiple",
                                    keyExpr: "id",
                                    selectedRowKeys: danhSachDotGuiSelectedIds,
                                    onSelectionChanged(keys) {
                                        setDanhSachDotGuiSelectedIds(keys)
                                    },
                                }}
                                filterRow={{
                                    enable: true
                                }}
                                exportEnable
                                actionComponent={
                                    <Box sx={{
                                        display: "flex"
                                    }}>
                                        <Button sx={{ ml: 2 }} variant="default" size="small" leadingVisual={SyncIcon} onClick={handleRefresh}>Làm mới</Button>
                                        <Button sx={{ ml: 2 }} variant="primary" size="small" leadingVisual={PlusIcon} onClick={handleInsertClick}>Thêm mới</Button>
                                        {danhSachDotGuiSelectedIds && danhSachDotGuiSelectedIds.length > 0 &&
                                            <Button sx={{ ml: 2 }} leadingVisual={TrashIcon} variant="danger" size="small" onClick={handleShowDeleteConfirm}>Xóa {danhSachDotGuiSelectedIds.length} đợt gửi TBTP</Button>
                                        }
                                    </Box>
                                }

                                columns={[
                                    {
                                        caption: "Thao tác",
                                        id: "actions",
                                        width: "100px",
                                        align: "center",
                                        cellRender: (data: sf_dotgui_tbtp_viewmodel) => (
                                            <span
                                                style={{ cursor: "pointer", color: "green" }}
                                                onClick={() => handleBtnEditClicked(data)}
                                            >
                                                <i className="fas fa-edit"></i>
                                            </span>
                                        ),
                                    },
                                    { caption: "Năm học", id: "nam_hoc", dataField: "nam_hoc", minWidth: 100 },
                                    {
                                        caption: "Tên đợt gửi", id: "ten_dot_gui", dataField: "ten_dot_gui", minWidth: 350,
                                        cellRender: (data) => <div style={{ whiteSpace: "pre-line" }}>{data.ten_dot_gui}</div>
                                    },
                                    {
                                        caption: "Ngày gửi",
                                        id: "ngay_gui",
                                        dataField: "ngay_gui",
                                        minWidth: 100,
                                        cellRender: (data: sf_dotgui_tbtp_viewmodel) => {
                                            return <> {moment(data.ngay_gui).format("DD/MM/YYYY")}</>;
                                        }
                                    }
                                    ,
                                    { caption: "Hình thức nộp bom", id: "sf_hinhthucnopbom_ids", dataField: "sf_hinhthucnopbom_ids", minWidth: 200, cellRender: hinhThucNopBomRender },
                                    { caption: "Nội dung email", id: "email_content", dataField: "email_content", minWidth: 100, align: 'center', cellRender: previewRender },
                                    { caption: "DS Khoản nộp", id: "id", dataField: "id", minWidth: 100, align: 'center', cellRender: previewKhoanNopRender },
                                    { caption: "Ghi chú", id: "ghi_chu", dataField: "ghi_chu", minWidth: 200 },
                                    {
                                        caption: "Tự động tính MG", id: "is_tinh_miengiam", dataField: "is_tinh_miengiam", minWidth: 100, align: 'center', cellRender: (data: any) => (
                                            <div style={{ display: "flex", justifyContent: "center" }}>
                                                <Checkbox
                                                    checked={data.is_tinh_miengiam === true}
                                                    aria-label="Tự động tính MG"
                                                />
                                            </div>
                                        ),
                                    },
                                    {
                                        caption: "Tự động thêm quyết toán", id: "is_tinh_quyettoan", dataField: "is_tinh_quyettoan", minWidth: 100, align: 'center', cellRender: (data: any) => (
                                            < div style={{ display: "flex", justifyContent: "center" }}>
                                                <Checkbox
                                                    checked={data.is_tinh_quyettoan === true}
                                                    aria-label="Tự động thêm quyết toán"
                                                />
                                            </div>
                                        ),
                                    },
                                ]}
                                paging={{
                                    enable: true,
                                    pageSizeItems: [50, 100, 200, 500]
                                }}
                            />
                            {is_show_detail_modal && <DotGuiCreateLayout
                                sf_dotgui_tbtp={sf_dotgui_tbtp_editing}
                                onClose={handleCloseDetailModal}
                                onSubmit={handleSubmitDetailModal}
                                is_saving={status === PageBaseStatus.is_saving}
                                title={`${sf_dotgui_tbtp_editing ? "Sửa " : "Thêm mới "} đợt gửi TBTP`}
                                animationOf={sf_dotgui_tbtp_editing ? `#btnEdit${sf_dotgui_tbtp_editing.id}` : "#btnInsert"}

                            />
                            }
                        </Box>

                    </Box>
                </Transition>
                {is_show_delete_confirm &&
                    <AnimationConfirm
                        onClose={handleCloseDeleteConfirm}
                        onConfirmed={handleConfirmDelete}
                        text={`Bạn có chắc chắn muốn xóa ${danhSachDotGuiSelectedIds.length} đợt gửi đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_deleting}
                        text_close_button="Không xóa"
                        text_confirm_button="Tiếp tục xóa"
                        type='danger'
                        animationOf='#btnDelete'

                    />
                }
            </Box>
            {isShowEmailContent &&
                <EmailContentModal
                    onClose={() => setIsShowEmailContent(false)}
                    onSubmit={() => setIsShowEmailContent(false)}
                    is_saving={isSaving}
                    animationOf='#btnEmail'
                    title="Mẫu email"
                    dataEmail={{ title_email: emailContent?.email_title || '', body_email: emailContent?.email_content || '' }}
                    isPreview={true}
                />
            }
            {
                isShowListKhoanNop &&
                <DanhSachKhoanNopModal
                    khoanNopSelectedIds={khoanNopSelectedIds}
                    onKhoanNopSelectChanged={setKhoanNopSelectedIds}
                    hinhThucNopBoms={hinhThucNopBomFilter}
                    onClose={() => setIsShowListKhoanNop(false)}
                    isInsert={false}
                    isPreview={true}
                    filter={filterData}
                />
            }

        </React.Fragment>
    </>)
}
export default DotGuiTBTPPage;