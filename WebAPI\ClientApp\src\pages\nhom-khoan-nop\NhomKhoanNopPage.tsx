﻿import {
    DownloadIcon,
    PlusIcon,
    SyncIcon,
    TrashIcon
} from "@primer/octicons-react";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { Transition } from 'semantic-ui-react';
// import { Button } from '../../components/buttons';
import { Box } from '@primer/react';
import { AnimationConfirm } from '../../components/confirm';
import Button from '../../components/ui/button';
import DataTable from "../../components/ui/data-table";
import Text from "../../components/ui/text";
import { PageBaseStatus } from '../../models/pageBaseStatus';
import { sf_nhomkhoannop } from '../../models/response/category/sf_nhomkhoannop';
import { closeNhomKhoanNopDetailModal, deleteNhomKhoanNopStart, loadNhomKhoanNopStart, saveNhomKhoanNopStart, showNhomKhoanNopDeleteConfirm, showNhomKhoanNopDetailModal } from '../../state/actions/nhomKhoanNopActions';
import { RootState } from '../../state/reducers';
import NhomKhoanNopDetailModal from './detail_modal';
import styles from "./NhomKhoanNop.module.css";
import NhomKhoanNopEditModal from "./NhomKhoanNopEditModal";

const NhomKhoanNopPage = () => {
    const state = useSelector((state: RootState) => state.nhomKhoanNop)
    const { dm_coso_id } = useSelector((state: RootState) => state.common)
    const [danhSachNhomKhoanNopSelectedIds, setDanhSachNhomKhoanNopSelectedIds] = useState<number[]>([]);
    const dispatch = useDispatch();
    const grid = useRef<any>();
    const {
        status,
        sf_nhomkhoannops,
        sf_nhomkhoannop_selected_ids,
        sf_nhomkhoannop_editing,
        is_show_detail_modal,
        is_show_delete_confirm
    } = state;

    const dataSource = useMemo(() => {
        return sf_nhomkhoannops.filter(x => x.dm_coso_id === dm_coso_id)
    }, [sf_nhomkhoannops, dm_coso_id])

    useEffect(() => {
        dispatch(loadNhomKhoanNopStart())
    }, [])

    const handleRefresh = useCallback(() => {
        dispatch(loadNhomKhoanNopStart());
        onSelectionChanged([])
        setDanhSachNhomKhoanNopSelectedIds([])
    }, [])

    const handleCloseDeleteConfirm = useCallback(() => {
        dispatch(showNhomKhoanNopDeleteConfirm(false))
    }, [])

    const handleShowDeleteConfirm = useCallback(() => {
        dispatch(showNhomKhoanNopDeleteConfirm(true))
    }, [])

    const handleBtnInsertClick = useCallback(() => {
        dispatch(showNhomKhoanNopDetailModal());
    }, []);

    const handleBtnEditClicked = (data: sf_nhomkhoannop) => {
        dispatch(showNhomKhoanNopDetailModal(data));
    }
    const handleExport = useCallback(() => {
        if (grid && grid.current && grid.current.instance) {
            grid.current.instance.exportToExcel();
        }
    }, [grid])

    // const onSelectionChanged = useCallback((selectedRowKeys: number[]) => {
    //     dispatch(changeNhomKhoanNopSelectionIds(selectedRowKeys))
    // }, [])

    const onSelectionChanged = ({ selectedRowKeys }: any) => {
        setDanhSachNhomKhoanNopSelectedIds(selectedRowKeys);

    };
    const handleConfirmDelete = useCallback(() => {
        dispatch(deleteNhomKhoanNopStart(danhSachNhomKhoanNopSelectedIds))
        setDanhSachNhomKhoanNopSelectedIds([])
    }, [danhSachNhomKhoanNopSelectedIds]);

    const handleCloseDetailModal = useCallback(() => {
        dispatch(closeNhomKhoanNopDetailModal())
    }, [])
    const handleSubmitDetailModal = useCallback((data: sf_nhomkhoannop) => {
        dispatch(saveNhomKhoanNopStart(data))
    }, [])
    return (
        <React.Fragment>
            <Box className={styles.container} sx={{ ml: 2 }}>
                <Transition animation="scale"
                    visible={true}
                    duration={100}
                    transitionOnMount={true}>
                    <Box>
                        <Box sx={{ m: 2 }}>
                            <DataTable
                                height="calc(100vh - 240px)"
                                titleComponent={<Text text="Danh sách nhóm khoản nộp" sx={{ fontSize: 16, fontWeight: 600 }} />}
                                data={dataSource}
                                emptyComponent={<Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        minHeight: "60vh",
                                        textAlign: "center"
                                    }}
                                >
                                    <img
                                        src="https://true-north-school.s3.cloudfly.vn/fee/datagrid_empty"
                                        alt="Không có dữ liệu"
                                        style={{ width: "60px", height: "60px", marginBottom: "5px" }}
                                    />
                                    <Box sx={{ fontSize: "13px", fontWeight: "400", color: "#8C8C8C" }}>
                                        Chưa có dữ liệu nhóm khoản nộp
                                    </Box>
                                </Box>
                                }
                                selection={{
                                    mode: "multiple",
                                    keyExpr: "id",
                                    selectedRowKeys: danhSachNhomKhoanNopSelectedIds,
                                    onSelectionChanged(keys) {
                                        setDanhSachNhomKhoanNopSelectedIds(keys)
                                    },
                                }}
                                exportEnable
                                filterRow={{
                                    enable: true
                                }}
                                actionComponent={
                                    <Box sx={{
                                        display: "flex"
                                    }}>
                                        <Button sx={{ ml: 2 }} variant="default" size="small" leadingVisual={SyncIcon} onClick={handleRefresh}>Làm mới</Button>
                                        <Button sx={{ ml: 2 }} variant="primary" size="small" leadingVisual={PlusIcon} onClick={handleBtnInsertClick}>Thêm mới</Button>
                                        {/* <Button sx={{ ml: 2 }} leadingVisual={DownloadIcon} variant="default" size="medium" onClick={handleExport}>Xuất excel</Button> */}
                                        {danhSachNhomKhoanNopSelectedIds && danhSachNhomKhoanNopSelectedIds.length > 0 &&
                                            <Button sx={{ ml: 2 }} leadingVisual={TrashIcon} variant="danger" size="small" onClick={handleShowDeleteConfirm}>Xóa {danhSachNhomKhoanNopSelectedIds.length} nhóm khoản nộp</Button>
                                        }
                                    </Box>
                                }
                                columns={[
                                    {
                                        caption: "Sửa",
                                        id: "edit",
                                        width: "50px",
                                        align: "center",
                                        isAllowFocus: false,
                                        cellRender: useCallback((data: any) => (
                                            <a id={`btnEdit${data.id}`} style={{ cursor: "pointer", color: "#3c22ff" }}>
                                                <i onClick={() => handleBtnEditClicked(data)} className="cmd-edit-icon fas fa-edit"></i>
                                            </a>
                                        ), [handleBtnEditClicked]),
                                    },
                                    {
                                        caption: "Mã nhóm",
                                        id: "ma_nhom",
                                        dataField: "ma_nhom",
                                        isAllowFocus: false,
                                    },

                                    {
                                        caption: "Nhóm khoản nộp",
                                        id: "nhom_khoan_nop",
                                        dataField: "nhom_khoan_nop",
                                        isAllowFocus: false,
                                    },
                                    {
                                        caption: "Thứ tự sắp xếp",
                                        id: "thutu_sapxep",
                                        dataField: "thutu_sapxep",
                                        isAllowFocus: false,
                                    },
                                ]}
                                paging={{
                                    enable: true,
                                    pageSizeItems: [50, 100, 200, 500]
                                }}
                            />
                            {is_show_detail_modal && <NhomKhoanNopEditModal
                                sf_nhomkhoannop={sf_nhomkhoannop_editing}
                                onClose={handleCloseDetailModal}
                                onSubmit={handleSubmitDetailModal}
                                is_saving={status === PageBaseStatus.is_saving}
                                title={`${sf_nhomkhoannop_editing ? "Cập nhật " : "Thêm mới "}`}
                                animationOf={sf_nhomkhoannop_editing ? `#btnEdit${sf_nhomkhoannop_editing.id}` : "#btnInsert"}
                            />
                            }
                        </Box>

                    </Box>
                </Transition>
                {is_show_delete_confirm &&
                    <AnimationConfirm
                        onClose={handleCloseDeleteConfirm}
                        onConfirmed={handleConfirmDelete}
                        text={`Bạn có chắc chắn muốn xóa ${danhSachNhomKhoanNopSelectedIds.length} khoản nộp đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_deleting}
                        text_close_button="Không xóa"
                        text_confirm_button="Xóa"
                        type='danger'
                        animationOf='#btnDelete'

                    />
                }
            </Box>
        </React.Fragment>
    );
}
export default NhomKhoanNopPage;