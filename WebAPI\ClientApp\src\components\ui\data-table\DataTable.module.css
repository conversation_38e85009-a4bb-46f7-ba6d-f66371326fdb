.container {
  border: 1px solid #d0d7de;
  border-radius: 0.375rem;

  position: relative;
  max-width: 100%;
  overflow-x: auto;
  overflow-y: auto;
  /* overflow-y: hidden; */
  display: block; /* <PERSON><PERSON><PERSON> bảo container hoạt động như một block */
}
.container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: block;
}
.container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.pagingContainer {
  position: sticky; /* Thay đổi từ absolute sang sticky */
  bottom: 0;
  background-color: #fff;
  left: 0;
  right: 0;
  padding: 8px;
  height: 40px;
  border-top: 1px solid #d0d7de;
  z-index: 1; /* <PERSON><PERSON><PERSON> bảo phân trang luôn nổi lên trên */
}

.selected {
  background-color: #0969da !important;
  color: #fff !important;
}

.title {
  font-size: 24px;
  font-weight: 600;
}

.subTitle {
  color: #1f2328;
  font-size: 12px;
}

.myTable {
  width: 100%;
  border-spacing: 0;
  border-collapse: separate;
  /* border: 1px solid #d0d7de; */
  border-radius: 0.375rem;
  table-layout: fixed;
}



.myTable.hasPaging {
  margin-bottom: 41px;
  border-bottom: 1px solid #d0d7de;
}

.myTable tbody {
  border-bottom: 1px solid #d0d7de;
}

.myTable tr:hover {
  background-color: #f5f8fa;
}

.fixed_column {
  position: sticky;
  left: 0;
  /* background-color: red; */
  z-index: 0;
  background-color: #fff;
}

.fixed_column_header {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 3 !important;
  background-color: #f5f8fa;
}

.myTable thead {
  font-weight: 500;
  background-color: #f5f8fa;
}

.myTable td {
  padding: 0.5rem 0.75rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 0;
  vertical-align: top;
}

.myTable th:not(:last-child),
.myTable td:not(:last-child) {
  border-right: 1px solid #d0d7de;
}

.myTable thead tr td {
  border-bottom: 1px solid #d0d7de;
  font-weight: 600;
  color: #656d76;
  padding: 0.5rem 0.75rem;
}

.myTable thead tr th {
  border-bottom: 1px solid #d0d7de;
  font-weight: 600;
  color: #656d76;
  padding: 0.5rem 0.75rem;

  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #f5f8fa;
}

/* Apply a border to the bottom of all but the last row */
.myTable > thead > tr:not(:last-child) > th,
.myTable > thead > tr:not(:last-child) > td,
.myTable > tbody > tr:not(:last-child) > th,
.myTable > tbody > tr:not(:last-child) > td,
.myTable > tfoot > tr:not(:last-child) > th,
.myTable > tfoot > tr:not(:last-child) > td,
.myTable > tr:not(:last-child) > td,
.myTable > tr:not(:last-child) > th,
.myTable > thead:not(:last-child),
.myTable > tbody:not(:last-child),
.myTable > tfoot:not(:last-child) {
  border-bottom: 1px solid #d0d7de;
}

.focused {
  /* background-color: red;        */
  padding: 0px !important;
  border-radius: 2px !important;
  /* border-color: royalblue; */
  /* outline: 2px; */
  /* border: 2px solid #0969da; */
  /* outline: 2px solid #0969da; */
  box-shadow: inset #0969da 0px 0px 0px 2px;
  /* overflow: hidden!important; */
}
.isDoneSave {
  background-color: #fff8c5;
}

.container {
  overflow-x: auto;
  overflow-y: auto;
  width: 100%;
}

.myTable {
  min-width: 100%;
  width: max-content; /* Đảm bảo bảng sẽ mở rộng theo nội dung */
}

/* CSS cho các cột cố định */
.fixed-column {
  position: sticky;
  left: 0; /* Cố định cột ở bên trái */
  z-index: 2; /* Đảm bảo cột cố định nằm trên các cột khác */
  background-color: white; /* Đảm bảo nền cột cố định luôn hiển thị đúng */
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1); /* Tạo hiệu ứng đổ bóng khi cuộn */
}

.container {
  position: relative;
  overflow: auto;
}

.myTable {
  position: relative;
}

.fixed_column {
  position: sticky !important;
  background: white;
  z-index: 1;
}

/* Tạo các class cho các vị trí fixed khác nhau */
.fixed_pos_0 {
  left: 0;
}
.fixed_pos_1 {
  left: 40px;
}
.fixed_pos_2 {
  left: 90px;
}
.fixed_pos_3 {
  left: 140px;
}
.fixed_pos_4 {
  left: 190px;
}
/* Thêm các vị trí khác nếu cần */

.fixed_column::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  box-shadow: inset -4px 0 4px -4px rgba(0, 0, 0, 0.15);
}
.footer {
  background-color: #f5f8fa;
  font-size: 12px;
  border-top: 1px solid #bfc2c5 !important;
}
.footer td div {
  margin: 2px 0; /* Khoảng cách giữa các dòng */
}
