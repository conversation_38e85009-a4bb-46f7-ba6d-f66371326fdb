import {
    CalendarIcon,
    CopyIcon,
    PencilIcon,
    PlusIcon,
    SyncIcon,
    TrashIcon
} from "@primer/octicons-react";
import clsx from 'clsx';

import { Box, Checkbox, Label } from '@primer/react';
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { Transition } from 'semantic-ui-react';
import { hinhThucMienGiamApi } from "../../api/hinhThucMienGiamApi";
import { hinhThucMienGiamGroupApi } from "../../api/hinhThucMienGiamGroupApi";
import { AnimationConfirm } from '../../components/confirm';
import SelectBoxMultipleNhomMienGiam from "../../components/selectbox-multiple-nhommiengiam";
import Button from "../../components/ui/button";
import MyIconButton from "../../components/ui/button/IconButton";
import DataTable from "../../components/ui/data-table";
import { PageBaseStatus } from '../../models/pageBaseStatus';
import { sf_hinhthucmiengiam } from '../../models/response/category/sf_hinhthucmiengiam';
import { sf_hinhthucmiengiam_hinhthuctinh } from "../../models/response/category/sf_hinhthucmiengiam_hinhthuctinh";
import { sf_hinhthucmiengiam_group } from "../../models/response/hinh-thuc-mien-giam/sf_hinhthucmiengiam_group";
import { changeHinhThucMienGiamNamHocAction, changeHinhThucMienGiamSelectionIds, closeHinhThucMienGiamCopyModalAction, closeHinhThucMienGiamDeleteConfirm, closeHinhThucMienGiamDetailModal, closeHinhThucMienGiamItemsModalAction, deleteHinhThucMienGiamStart, loadHinhThucMienGiamStart, saveHinhThucMienGiamStart, showHinhThucMienGiamCopyModalAction, showHinhThucMienGiamDeleteConfirm, showHinhThucMienGiamDetailModal, showHinhThucMienGiamItemsModalAction } from '../../state/actions/hinhThucMienGiamActions';
import { RootState } from '../../state/reducers';
import { MienGiamCopyModal } from './copy_modal';
import { MienGiamItemsModal } from './detail_items';
import HinhThucMienGiamEditModal from "./HinhThucMienGiamEditModal";
import styles from "./style.module.css";

const HinhThucMienGiamPage = () => {
    const state = useSelector((state: RootState) => state.hinhThucMienGiam)
    const { nam_hoc: param_nam_hoc, dm_coso_id } = useSelector((x: RootState) => x.common)
    const [sf_hinhthucmiengiam_group, set_sf_hinhthucmiengiam_group] = useState<sf_hinhthucmiengiam_group[]>([])
    const [danhSachMienGiamSelectId, setDanhSachMienGiamSelectId] = useState<number[]>([])
    const [sf_hinhthucmiengiam, set_sf_hinhthucmiengiam] = useState<sf_hinhthucmiengiam>()
    const [isShowItemModal, setIsShowItemModal] = useState<boolean>(false)
    const [nhomMienGiamFilter, setNhomMienGiamFilter] = useState<number[]>([]);
    const [hinhthucmiengiam_hinhthuctinhs, set_hinhthucmiengiam_hinhthuctinhs] = useState<sf_hinhthucmiengiam_hinhthuctinh[]>([])

    const dispatch = useDispatch();
    const {
        status,
        sf_hinhthucmiengiam_selected_ids,
        sf_hinhthucmiengiams,
        sf_hinhthucmiengiam_editing,
        is_show_detail_modal,
        is_show_delete_confirm,
        nam_hoc,
        sf_hinhthucmiengiam_hinhthuctinhs,
        is_show_copy_modal,
        is_show_items_modal
    } = state;
    useEffect(() => {
        dispatch(changeHinhThucMienGiamNamHocAction(param_nam_hoc))
    }, [param_nam_hoc])
    useEffect(() => {
        handleReloadHinhThucMienGiamGroup();
        dispatch(loadHinhThucMienGiamStart(nam_hoc))
        dispatch(closeHinhThucMienGiamItemsModalAction())
        handleReloadHinhThucTinh();
    }, [nam_hoc])

    useEffect(() => {
        handleReloadHinhThucMienGiamGroup();
        dispatch(loadHinhThucMienGiamStart(nam_hoc))
        handleReloadHinhThucTinh();
    }, [])

    const handleCloseDeleteConfirm = useCallback(() => {
        dispatch(closeHinhThucMienGiamDeleteConfirm())
    }, [])
    const handleShowDeleteConfirm = useCallback(() => {
        dispatch(showHinhThucMienGiamDeleteConfirm())
    }, [])

    const handleBtnInsertClick = useCallback((e: any) => {
        if (e.itemData.id === 0) {
            dispatch(showHinhThucMienGiamDetailModal());
        }
        if (e.itemData.id === 1) {
            dispatch(showHinhThucMienGiamCopyModalAction());
        }

    }, []);
    const handleBtnEditClicked = (data: sf_hinhthucmiengiam) => {
        dispatch(showHinhThucMienGiamDetailModal(data));
    }

    const handleConfirmDelete = useCallback(() => {
        dispatch(deleteHinhThucMienGiamStart(danhSachMienGiamSelectId))
        setTimeout(() => {
            dispatch(loadHinhThucMienGiamStart(nam_hoc));
        }, 500);
    }, [dispatch, danhSachMienGiamSelectId]);

    const handleCloseDetailModal = useCallback(() => {
        dispatch(closeHinhThucMienGiamDetailModal())
        dispatch(loadHinhThucMienGiamStart(nam_hoc))

    }, [dispatch])
    const handleSubmitDetailModal = useCallback((data: sf_hinhthucmiengiam) => {
        dispatch(saveHinhThucMienGiamStart(data))
        dispatch(changeHinhThucMienGiamSelectionIds([]))
        dispatch(loadHinhThucMienGiamStart(nam_hoc))

    }, [dispatch])

    const handleShowItemsModal = useCallback((sf_hinhthucmiengiam: sf_hinhthucmiengiam) => {
        dispatch(showHinhThucMienGiamItemsModalAction(sf_hinhthucmiengiam))
    }, [])
    const handleCloseItemsModal = useCallback(() => {
        dispatch(closeHinhThucMienGiamItemsModalAction())
    }, [])

    const handleReloadHinhThucMienGiamGroup = async () => {
        const res = await hinhThucMienGiamGroupApi.selectAll();
        if (res.is_success) {
            set_sf_hinhthucmiengiam_group(res.data.filter((x: any) => x.dm_coso_id == dm_coso_id));
        }
    }
    const handleReloadHinhThucTinh = async () => {
        const res = await hinhThucMienGiamApi.select_hinhthuctinhs();
        if (res.is_success) {
            set_hinhthucmiengiam_hinhthuctinhs(res.data);
        }
    }
    const dataSource = useMemo(() => {
        let sortAppends = [...sf_hinhthucmiengiams.filter(x => x.dm_coso_id === dm_coso_id && x.nam_hoc === nam_hoc)]
        if (nhomMienGiamFilter.length > 0) {
            sortAppends = sortAppends.filter(x => nhomMienGiamFilter.includes(x.sf_hinhthucmiengiam_group_id))
        }
        return sortAppends;
    }, [sf_hinhthucmiengiams, nhomMienGiamFilter, dm_coso_id, nam_hoc]);
    const cellRender = (data: any) => {
        return (
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-start', color: 'red' }}>
                <MyIconButton aria-label={"Edit"} icon={PencilIcon}
                    variant="default"
                    size="small"
                    onClick={() => {
                        handleBtnEditClicked(data);
                    }}
                ></MyIconButton>
                <MyIconButton sx={{ ml: 1 }} aria-label={"Clear"} icon={TrashIcon}
                    disabled={data.is_apdung_toantruong}
                    variant="danger"
                    size="small"
                    onClick={() => {
                        setDanhSachMienGiamSelectId([data.id])
                        handleShowDeleteConfirm();
                    }}
                ></MyIconButton>
            </Box>
        );
    };
    const columns = useMemo(() => {
        const currentDate = new Date();
        let result: any = [
            {
                id: "actions",
                caption: "#",
                width: "50px",
                align: "center",
                cellRender: (cellData: any) => {
                    return cellRender(cellData);
                },
            },
            {
                caption: "Nhóm miễn giảm",
                id: "sf_hinhthucmiengiam_group_id",
                dataField: "sf_hinhthucmiengiam_group_id",
                width: "140px",
                cellRender: (data: any) => {
                    const group = sf_hinhthucmiengiam_group
                        .filter((c) => c.dm_coso_id === dm_coso_id)
                        .find((g) => g.id === data.sf_hinhthucmiengiam_group_id);
                    return <span>{`(${group?.thu_tu}) ${group?.ten_nhom_mien_giam}` || ""}</span>;
                },
            },
            {
                caption: "Mã miễn giảm",
                id: "ma_hinh_thuc",
                dataField: "ma_hinh_thuc",
                width: "120px",
                alignment: "left",
                cellRender: (data: any) => {
                    return <Box>
                        <span style={{ fontWeight: 700, marginLeft: 2 }}>
                            <Label variant="accent">{data.ma_nhom_mien_giam}-{data.ma_hinh_thuc}</Label>
                        </span>
                    </Box>

                }
            },
            {
                caption: "Tên miễn giảm",
                id: "hinh_thuc_mien_giam",
                dataField: "hinh_thuc_mien_giam",
                minWidth: "150px",
                alignment: "left",
                cellRender: (data: any) => (
                    <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                        <span style={{ fontWeight: 600 }}>{data.hinh_thuc_mien_giam}</span>
                    </div>
                ),
            },
            {
                caption: "Hình thức tính",
                id: "sf_hinhthucmiengiam_hinhthuctinh_id",
                dataField: "sf_hinhthucmiengiam_hinhthuctinh_id",
                width: "150px",
                alignment: "left",
                cellRender: (data: any) => {
                    const group = hinhthucmiengiam_hinhthuctinhs
                        .find((g) => g.id === data.sf_hinhthucmiengiam_hinhthuctinh_id);
                    return <span>{group?.hinh_thuc_tinh || ""}</span>;
                },
            },
            {
                caption: "Thời gian áp dụng",
                id: "tu_ngay",
                dataField: "tu_ngay",
                width: "200px",
                alignment: "left",
                cellRender: (data: any) => {
                    const formatDate = (date: string | Date | null) => {
                        if (!date) return "";
                        return new Intl.DateTimeFormat("vi-VN", {
                            day: "2-digit",
                            month: "2-digit",
                            year: "numeric",
                        }).format(new Date(date));
                    };
                    return (<Box>
                        <div style={{ display: "flex", alignItems: "center", gap: "5px", fontWeight: 500 }}>
                            <span style={{ color: "green" }}><CalendarIcon size={16} /></span>
                            <span>{formatDate(data.tu_ngay)}</span>
                            <span>-</span>
                            <span style={{ color: "red" }}><CalendarIcon size={16} /></span>
                            <span>{formatDate(data.den_ngay)}</span>
                        </div>
                    </Box>)
                },
            },
            {
                caption: "Trạng thái",
                id: "den_ngay",
                dataField: "den_ngay",
                width: "100px",
                cellRender: (data: any) => {
                    const endDate = new Date(data.den_ngay);
                    return (
                        <Box>
                            {endDate > currentDate ? (
                                <Label variant="success">Khả dụng</Label>
                            ) : (
                                <Label variant="danger">Hết hạn</Label>
                            )}
                        </Box>
                    );
                },
            },

            {
                caption: "Áp dụng toàn trường",
                id: "is_apdung_toantruong",
                dataField: "is_apdung_toantruong",
                width: "140px",
                cellRender: (data: any) => (
                    <div style={{ display: "flex", justifyContent: "center" }}>
                        <Checkbox checked={data.is_apdung_toantruong === true} aria-label="Áp dụng toàn trường" />
                    </div>
                ),
            },
        ];
        return result;

    }, [sf_hinhthucmiengiams, hinhthucmiengiam_hinhthuctinhs, sf_hinhthucmiengiam_group]);
    return (
        <React.Fragment>
            <Box className={styles.container} sx={{ pl: 1 }}>
                <Transition animation="scale"
                    visible={true}
                    duration={100}
                    transitionOnMount={true}>
                    <div>
                        <div className="row">
                            <div className="col-md-12">
                                <div className={styles.main_container}>
                                    <div className={styles.left_panel}>

                                        <Box className="col-md-12" sx={{ mt: 2 }}>
                                            <DataTable
                                                // titleComponent={<Text text={`Danh sách hình thức miễn giảm ${nam_hoc}`} sx={{ fontSize: 16, fontWeight: 600 }} />}
                                                data={dataSource}
                                                subTitle={`Tổng số: ${dataSource.length}`}
                                                height="calc(100vh - 300px)"
                                                emptyComponent={
                                                    <Box
                                                        sx={{
                                                            display: "flex",
                                                            flexDirection: "column",
                                                            alignItems: "center",
                                                            justifyContent: "center",
                                                            minHeight: "60vh",
                                                            textAlign: "center",
                                                        }}
                                                    >
                                                        <img
                                                            src="https://true-north-school.s3.cloudfly.vn/fee/datagrid_empty"
                                                            alt="Không có dữ liệu"
                                                            style={{ width: "60px", height: "60px", marginBottom: "5px" }}
                                                        />
                                                        <Box sx={{ fontSize: "13px", fontWeight: "400", color: "#8C8C8C" }}>
                                                            Không có dữ liệu
                                                        </Box>
                                                    </Box>
                                                }
                                                filterRow={{
                                                    enable: true
                                                }}
                                                selection={{
                                                    mode: "single",
                                                    keyExpr: "id",
                                                    selectedRowKeys: danhSachMienGiamSelectId,
                                                    onSelectionChanged(keys) {
                                                        setDanhSachMienGiamSelectId(keys)
                                                    },
                                                }}
                                                onClick={(e) => {
                                                    setDanhSachMienGiamSelectId(e.rowData.id)
                                                    set_sf_hinhthucmiengiam(e.rowData)
                                                    setIsShowItemModal(true)
                                                }}
                                                searchEnable
                                                actionComponent={
                                                    <Box sx={{ display: "flex" }}>
                                                        <Box sx={{ ml: 2 }}>
                                                            <SelectBoxMultipleNhomMienGiam
                                                                value={nhomMienGiamFilter}
                                                                onValueChanged={(value) => {
                                                                    setNhomMienGiamFilter(value === null ? [] : value);
                                                                }}
                                                            />
                                                        </Box>
                                                        <Button sx={{ ml: 2 }} variant="default" leadingVisual={SyncIcon} onClick={() => dispatch(loadHinhThucMienGiamStart(nam_hoc))
                                                        }>Làm mới</Button>
                                                        <Button sx={{ ml: 2 }} variant="primary" leadingVisual={PlusIcon} onClick={() => {
                                                            dispatch(showHinhThucMienGiamDetailModal(undefined));
                                                            set_sf_hinhthucmiengiam(undefined)
                                                            dispatch(showHinhThucMienGiamDetailModal())
                                                        }}
                                                        >Thêm mới</Button>
                                                        <Button sx={{ ml: 2 }} leadingVisual={CopyIcon} variant="primary" onClick={() => dispatch(showHinhThucMienGiamCopyModalAction())}>Sao chép từ năm học cũ</Button>
                                                    </Box>
                                                }
                                                columns={columns}
                                                paging={{
                                                    enable: true,
                                                    pageSizeItems: [50, 200, 500, 1000],
                                                }}
                                            />
                                        </Box>
                                    </div>
                                    <div className={clsx(styles.right_panel,
                                        danhSachMienGiamSelectId.length === 0 ? styles.right_panel_placeholder : "")}>
                                        {danhSachMienGiamSelectId.length === 0 && <>
                                            <div style={{ padding: "1rem", textAlign: "center" }}>
                                                <h5>Chọn 1 Hình thức miễn giảm từ danh sách bên trái để xem và điều chỉnh khoản miễn giảm</h5>
                                            </div>
                                        </>}
                                        {(isShowItemModal && sf_hinhthucmiengiam) &&
                                            <MienGiamItemsModal
                                                sf_hinhthucmiengiam={sf_hinhthucmiengiam}
                                                animationOf={`#btnItem${sf_hinhthucmiengiam.id}`}
                                                onClose={handleCloseItemsModal}
                                                onEditRequest={() => {
                                                    handleBtnEditClicked(sf_hinhthucmiengiam)
                                                }}
                                            />}
                                    </div>
                                </div>
                            </div>


                        </div>

                    </div>
                </Transition>
                {is_show_detail_modal &&
                    <HinhThucMienGiamEditModal
                        onClose={handleCloseDetailModal}
                        onSubmit={handleSubmitDetailModal}
                        sf_hinhthucmiengiam={sf_hinhthucmiengiam}
                        is_saving={status === PageBaseStatus.is_saving}
                        nam_hoc={nam_hoc}
                        animationOf={sf_hinhthucmiengiam ? `#btnEdit` : "#btnInsert"}
                        title={sf_hinhthucmiengiam ? `Cập nhật` : "Thêm mới"}

                    />}
                {is_show_delete_confirm &&
                    <AnimationConfirm
                        onClose={handleCloseDeleteConfirm}
                        onConfirmed={handleConfirmDelete}
                        text={`Bạn có chắc chắn muốn xóa hình thức miễn giảm đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_deleting}
                        text_close_button="Không xóa"
                        text_confirm_button="Xóa miễn giảm"
                        type='danger'
                        animationOf={`#ListItem${danhSachMienGiamSelectId[0]}`}


                    />
                }

                {is_show_copy_modal && nam_hoc &&
                    <MienGiamCopyModal
                        nam_hoc_to={nam_hoc}
                        animationOf="#btnInsert"
                        onClose={() => { dispatch(closeHinhThucMienGiamCopyModalAction()) }}
                        onSuccess={() => {
                            dispatch(closeHinhThucMienGiamCopyModalAction());
                            dispatch(loadHinhThucMienGiamStart(nam_hoc));
                        }}
                    />
                }
            </Box>
        </React.Fragment >
    );
}
export default HinhThucMienGiamPage;