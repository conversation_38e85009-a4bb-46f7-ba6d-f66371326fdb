import {
    PlusIcon,
    SyncIcon,
    TrashIcon
} from '@primer/octicons-react';
import { Box, Checkbox } from '@primer/react';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { hinhThucNopBomApi } from '../../api/hinhThucNopBom';
import { AnimationConfirm } from '../../components/confirm';
import SelectBoxDichVu from '../../components/selectbox-dichvu-multiple';
import SelectBoxMultipleKhomKhoanNop from '../../components/selectbox-nhomkhoannop';
import Button from '../../components/ui/button';
import DataTable from '../../components/ui/data-table';
import { eSortMode } from '../../components/ui/data-table/DataTable';
import Text from '../../components/ui/text';
import { useCommonContext } from '../../contexts/common';
import { PageBaseStatus } from '../../models/pageBaseStatus';
import { sf_hinhthucnopbom } from '../../models/response/category/sf_hinhthucnopbom';
import { sf_khoannop } from '../../models/response/category/sf_khoannop';
import { changeKhoanNopSelectionIds, closeKhoanNopDeleteConfirm, closeKhoanNopDetailModal, deleteKhoanNopStart, loadKhoanNopStart, saveKhoanNopStart, showKhoanNopDeleteConfirm, showKhoanNopDetailModal } from '../../state/actions/khoanNopActions';
import { loadKhoanNopGroupStart } from '../../state/actions/khoanNopGroupActions';
import { loadLoaiKhoanNopStart } from '../../state/actions/loaiKhoanNopActions';
import { RootState } from '../../state/reducers';
import KhoanNopEditModal from './KhoanNopEditModal';
import styles from './KhoanNopPage.module.css';

const DanhSachKhoanNopPage = () => {
    const { checkAccesiableTo, getDataGridStorageKey, createUUID } = useCommonContext();
    const state = useSelector((state: RootState) => state.khoanNop)
    const { dm_coso_id } = useSelector((state: RootState) => state.common)
    const sf_loaikhoannops = useSelector((x: RootState) => x.loaiKhoanNop.sf_loaikhoannops)
    const sf_khoannop_groups = useSelector((x: RootState) => x.khoanNopGroup.sf_khoannop_groups)
    const [hinhThucNopBoms, setHinhThucNopBoms] = useState<sf_hinhthucnopbom[]>([]);
    const [danhSachKhoanNopSelectedIds, setDanhSachKhoanNopSelectedIds] = useState<number[]>([]);
    const [khoanNopGroupFilter, setKhoanNopGroupFilter] = useState<number[]>([]);
    const [nhomKhoanNopFilter, setNhomKhoanNopFilter] = useState<number[]>([]);

    const dispatch = useDispatch();
    const {
        status,
        sf_khoannops,
        sf_khoannop_selected_ids,
        sf_khoannop_editing,
        is_show_detail_modal,
        is_show_delete_confirm
    } = state;
    const dataSource = useMemo(() => {
        let sortAppends = [...sf_khoannops]
        if (nhomKhoanNopFilter.length > 0) {
            sortAppends = sortAppends.filter(x => nhomKhoanNopFilter.includes(x.sf_nhomkhoannop_id))
        }
        if (khoanNopGroupFilter.length > 0) {
            sortAppends = sortAppends.filter(x => khoanNopGroupFilter.includes(x.sf_khoannop_group_id))
        }
        const sortResult = sortAppends
            .filter(x => x.dm_coso_id === dm_coso_id)
            .sort((a, b) => {
                // 1. So sánh thutu_sapxep_nhomkhoannop
                const thuTuNhomA = Number(a.thutu_sapxep_nhomkhoannop) || 0;
                const thuTuNhomB = Number(b.thutu_sapxep_nhomkhoannop) || 0;
                if (thuTuNhomA !== thuTuNhomB) {
                    return thuTuNhomA - thuTuNhomB; // Tăng dần
                }

                // 2. Nếu thutu_sapxep_nhomkhoannop bằng nhau, so sánh thutu_sapxep
                const thuTuA = Number(a.thutu_sapxep) || 0;
                const thuTuB = Number(b.thutu_sapxep) || 0;
                return thuTuA - thuTuB; // Tăng dần
            });
        return sortResult;
    }, [sf_khoannops, dm_coso_id, nhomKhoanNopFilter, khoanNopGroupFilter]);
    useEffect(() => {
        dispatch(loadKhoanNopGroupStart())
        dispatch(loadKhoanNopStart())
        setDanhSachKhoanNopSelectedIds([])
    }, [])
    useEffect(() => {
        if (state.status === PageBaseStatus.is_need_reload) {
            dispatch(loadKhoanNopStart())
        }
    }, [state])
    useEffect(() => {
        if (sf_loaikhoannops.length == 0) dispatch(loadLoaiKhoanNopStart())
        handleReloadHinhThucNopBoms();

    }, [sf_loaikhoannops])

    useEffect(() => {
        if (hinhThucNopBoms.length == 0) {
            handleReloadHinhThucNopBoms();
        }

    }, [hinhThucNopBoms])
    useEffect(() => {
        dispatch(loadKhoanNopStart())
        setDanhSachKhoanNopSelectedIds([])

    }, [])
    const handleReloadHinhThucNopBoms = async () => {
        const res = await hinhThucNopBomApi.selectAll();
        if (res.is_success) {
            setHinhThucNopBoms(res.data.filter((x: any) => x.id > 0))
        }
    }
    const handleRefresh = useCallback(() => {
        dispatch(loadKhoanNopStart());
        onSelectionChanged([])
        setDanhSachKhoanNopSelectedIds([])

    }, [])

    const handleCloseDeleteConfirm = useCallback(() => {
        dispatch(closeKhoanNopDeleteConfirm())
    }, [])
    const handleShowDeleteConfirm = useCallback(() => {
        dispatch(showKhoanNopDeleteConfirm())
    }, [])

    const handleBtnInsertClick = useCallback(() => {
        dispatch(showKhoanNopDetailModal());
    }, []);
    const handleBtnEditClicked = (data: sf_khoannop) => {
        dispatch(showKhoanNopDetailModal(data));
    }
    const handleConfirmDelete = useCallback(() => {
        dispatch(deleteKhoanNopStart(danhSachKhoanNopSelectedIds))
        setDanhSachKhoanNopSelectedIds([])

    }, [danhSachKhoanNopSelectedIds]);

    const handleCloseDetailModal = useCallback(() => {
        dispatch(closeKhoanNopDetailModal())
    }, [])
    const handleSubmitDetailModal = useCallback((data: sf_khoannop) => {
        dispatch(saveKhoanNopStart(data))
        setDanhSachKhoanNopSelectedIds([])
    }, [])
    const hinhThucNopBomRender = (cell: any) => {
        const ids = cell.sf_hinhthucnopbom_ids.split(',');
        const names: string[] = [];
        ids.forEach((id: any) => {
            const hinhThucNopBom = hinhThucNopBoms.find((item: any) => item.id.toString() === id.trim());
            if (hinhThucNopBom) {
                names.push(hinhThucNopBom.name);
            }
        });
        return (
            <div>
                {names.join(', ')}
            </div>
        );
    };
    const onSelectionChanged = ({ selectedRowKeys }: any) => {
        dispatch(changeKhoanNopSelectionIds(selectedRowKeys))

    };
    const columns = useMemo(() => {
        let result: any = [
            {
                caption: "#",
                id: "id",
                dataField: "id",
                width: "50px",
                align: "center",
                cellRender: (data: any) => {
                    return (
                        <div>
                            <span
                                style={{ cursor: "pointer", color: "green" }}
                                onClick={() => {
                                    handleBtnEditClicked(data);
                                }}
                            >
                                <i aria-hidden="true" className="icon edit"></i>{" "}
                            </span>
                        </div>
                    );
                }
            },
            {
                caption: "Áp dụng",
                id: "is_active",
                dataField: "is_active",
                width: "70px",
                cellRender: (data: any) => (
                    <div style={{ display: "flex", justifyContent: "center" }}>
                        <Checkbox checked={data.is_active === true} aria-label="Áp dụng" />
                    </div>
                ),
            },
            {
                caption: "Nhóm",
                id: "nhom_khoan_nop",
                dataField: "nhom_khoan_nop",
                width: "140px",

            },
            {
                caption: "Thứ tự",
                id: "thutu_sapxep",
                dataField: "thutu_sapxep",
                width: "80px",
                sortOrder: "asc",
                cellRender: (data: any) => (
                    <span>{`${data.thutu_sapxep_nhomkhoannop}.${data.thutu_sapxep}`}</span>
                ),
            },
            {
                caption: "Mã khoản nộp",
                id: "ma_khoan_nop",
                dataField: "ma_khoan_nop",
                width: "120px",
                alignment: "left",
                isMainColumn: true
            },
            {
                caption: "Tên khoản nộp",
                id: "ten_khoan_nop",
                dataField: "ten_khoan_nop",
                minWidth: "150px",
                alignment: "left",
                isMainColumn: true
            },
            {
                caption: "Nhóm khoản nộp TBTP",
                id: "sf_khoannop_group_id",
                dataField: "sf_khoannop_group_id",
                width: "200px",
                cellRender: (data: any) => {
                    const group = sf_khoannop_groups
                        .filter((c) => c.dm_coso_id === dm_coso_id)
                        .find((g) => g.id === data.sf_khoannop_group_id);
                    return <span>{group?.ten_khoan_nop_group || ""}</span>;
                },
            },
            {
                caption: "Loại khoản nộp",
                id: "sf_loaikhoannop_id",
                dataField: "sf_loaikhoannop_id",
                width: "150px",
                cellRender: (data: any) => {
                    const type = sf_loaikhoannops.find((t) => t.id === data.sf_loaikhoannop_id);
                    return <span>{type?.loai_khoan_nop || ""}</span>;
                },
            },
            {
                caption: "Áp dụng cho TBTP",
                id: "sf_hinhthucnopbom_ids",
                dataField: "sf_hinhthucnopbom_ids",
                width: "250px",
                alignment: "left",
                cellRender: hinhThucNopBomRender,
            },
            {
                caption: "Đơn vị tính",
                id: "DVT",
                dataField: "DVT",
                width: "100px",
                alignment: "left",
            },
            {
                caption: "Thu đầu vào",
                id: "thu_dau_vao",
                dataField: "thu_dau_vao",
                width: "100px",
                cellRender: (data: any) => (
                    <div style={{ display: "flex", justifyContent: "center" }}>
                        <Checkbox checked={data.thu_dau_vao === true} aria-label="Thu đầu vào" />
                    </div>
                ),
            },
            {
                caption: "Khoản tạm thu",
                id: "is_khoan_tam_thu",
                dataField: "is_khoan_tam_thu",
                width: "100px",
                cellRender: (data: any) => (
                    <div style={{ display: "flex", justifyContent: "center" }}>
                        <Checkbox checked={data.is_khoan_tam_thu === true} aria-label="Khoản tạm thu" />
                    </div>
                ),
            },
            {
                caption: "Dịch vụ",
                id: "is_dichvu",
                dataField: "is_dichvu",
                width: "100px",
                cellRender: (data: any) => (
                    <div style={{ display: "flex", justifyContent: "center" }}>
                        <Checkbox checked={data.is_dichvu === true} aria-label="Dịch vụ" />
                    </div>
                ),
            },
        ];

        return result;
    }, [sf_khoannop_groups, dm_coso_id, sf_loaikhoannops, hinhThucNopBomRender]);

    return (
        <React.Fragment>
            <Box className={styles.container} sx={{ p: 2 }}>
                <Box className="col-md-12" sx={{ mt: 2 }}>
                    <DataTable
                        titleComponent={<Text text="Danh sách khoản nộp" sx={{ fontSize: 16, fontWeight: 600 }} />}
                        data={dataSource}
                        height="calc(100vh - 250px)"
                        sortConfig={{ enable: false, mode: eSortMode.ASC }}
                        emptyComponent={
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    minHeight: "60vh",
                                    textAlign: "center",
                                }}
                            >
                                <img
                                    src="https://true-north-school.s3.cloudfly.vn/fee/datagrid_empty"
                                    alt="Không có dữ liệu"
                                    style={{ width: "60px", height: "60px", marginBottom: "5px" }}
                                />
                                <Box sx={{ fontSize: "13px", fontWeight: "400", color: "#8C8C8C" }}>
                                    Không có dữ liệu
                                </Box>
                            </Box>
                        }
                        selection={{
                            mode: "multiple",
                            keyExpr: "id",
                            selectedRowKeys: danhSachKhoanNopSelectedIds,
                            onSelectionChanged(keys) {
                                setDanhSachKhoanNopSelectedIds(keys)
                            },
                        }}
                        exportEnable
                        filterRow={{
                            enable: true
                        }}
                        actionComponent={
                            <Box sx={{ display: "flex" }}>
                                <Box sx={{ ml: 2 }}>
                                    <SelectBoxMultipleKhomKhoanNop
                                        value={nhomKhoanNopFilter}
                                        onValueChanged={(value) => {
                                            setNhomKhoanNopFilter(value === null ? [] : value);
                                        }}
                                        size="small"
                                    />
                                </Box>
                                <Box sx={{ ml: 2 }}>
                                    <SelectBoxDichVu
                                        value={khoanNopGroupFilter}
                                        onValueChanged={(value) => {
                                            setKhoanNopGroupFilter(value === null ? [] : value);
                                        }}
                                        isdichvu={false}
                                        size="small"

                                    />
                                </Box>
                                <Button sx={{ ml: 2 }} variant="default" size='small' leadingVisual={SyncIcon} onClick={handleRefresh}>Làm mới</Button>
                                <Button sx={{ ml: 2 }} variant="primary" size='small' leadingVisual={PlusIcon} onClick={handleBtnInsertClick}>Thêm mới</Button>
                                {danhSachKhoanNopSelectedIds && danhSachKhoanNopSelectedIds.length > 0 &&
                                    <Button sx={{ ml: 2 }} leadingVisual={TrashIcon} variant="danger" size='small' onClick={handleShowDeleteConfirm}>Xóa {danhSachKhoanNopSelectedIds.length} khoản nộp</Button>
                                }
                            </Box>
                        }
                        columns={columns}
                        paging={{
                            enable: true,
                            pageSizeItems: [50, 100, 200, 500],
                        }}
                    />
                </Box>
                {is_show_detail_modal && <KhoanNopEditModal
                    sf_khoannop={sf_khoannop_editing}
                    onClose={handleCloseDetailModal}
                    onSubmit={handleSubmitDetailModal}
                    is_saving={status === PageBaseStatus.is_saving}
                    title={`${sf_khoannop_editing ? "Sửa " : "Thêm mới "}khoản nộp`}
                    animationOf={sf_khoannop_editing ? `#btnEdit${sf_khoannop_editing.id}` : "#btnInsert"}
                />
                }
                {is_show_delete_confirm &&
                    <AnimationConfirm
                        onClose={handleCloseDeleteConfirm}
                        onConfirmed={handleConfirmDelete}
                        text={`Bạn có chắc chắn muốn xóa khoản nộp đã chọn ?`}
                        is_saving={status === PageBaseStatus.is_deleting}
                        text_close_button="Không xóa"
                        text_confirm_button="Xóa khoản nộp"
                        type='danger'
                        animationOf='#btnDelete'

                    />
                }
            </Box >

        </React.Fragment>

    );
}
export default DanhSachKhoanNopPage