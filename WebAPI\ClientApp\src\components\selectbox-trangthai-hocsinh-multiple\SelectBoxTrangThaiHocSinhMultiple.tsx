import { TriangleDownIcon, XCircleFillIcon } from '@primer/octicons-react';
import { Box, Button, Checkbox, FormControl, SelectPanel } from '@primer/react';
import { useEffect, useMemo, useState } from 'react';
import { useCommonContext } from '../../contexts/common';
import { hocSinhApi } from '../../api/hocSinhApi';

interface ISelectBoxTrangThaiHocSinhMultipleProps {
    selectedValue: number[];
    onSelectionChanged: (dm_TrangThaiHocSinh_ids: number[]) => void;
    maxWidth?: number;
    isShowClearBtn?: boolean;
    hocSinhs?: any[]; // Thêm prop này để nhận dữ liệu học sinh
    size?: "small" | "medium" | "large";
}

const SelectBoxTrangThaiHocSinhMultiple = (props: ISelectBoxTrangThaiHocSinhMultipleProps) => {
    const { translate } = useCommonContext();
    const [dm_TrangThaiHocSinhs, setDmTrangThaiHocSinhs] = useState<any[]>([]);
    const [open, setOpen] = useState(false);
    const [filter, setFilter] = useState('');

    // Tính toán số lượng cho mỗi trạng thái - tính một lần và không thay đổi
    const counts = useMemo(() => {
        const counts: { [key: number]: number } = {};
        if (props.hocSinhs && props.hocSinhs.length > 0) {
            props.hocSinhs.forEach(hs => {
                const statusId = hs.dm_trangthaihocsinh_id;
                counts[statusId] = (counts[statusId] || 0) + 1;
            });
        }
        return counts;
    }, [props.hocSinhs]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const res = await hocSinhApi.selectTrangThai();
                if (res.is_success) {
                    setDmTrangThaiHocSinhs(res.data);
                    console.log('Dữ liệu trạng thái:', res.data);
                }
            } catch (error) {
                console.error('Lỗi khi lấy dữ liệu:', error);
            }
        };
        fetchData();
    }, []);

    // Tạo dataSource với số lượng cố định
    const dataSource = useMemo(() => {
        return (dm_TrangThaiHocSinhs ?? []).map(x => ({
            id: x.id,
            text: `${x.trang_thai}`,
            originalText: x.trang_thai,
            count: counts[x.id] || 0
        }));
    }, [dm_TrangThaiHocSinhs, counts]);

    const filterdData = useMemo(() => {
        return dataSource.filter(item =>
            item.originalText.toLowerCase().includes(filter.toLowerCase())
        );
    }, [dataSource, filter]);

    const _selectedDatas = useMemo(() => {
        return dataSource.filter(item => props.selectedValue.includes(item.id));
    }, [props.selectedValue, dataSource]);

    const isSelectedAll = filterdData.length > 0 &&
        filterdData.map(x => x.id).find(id => !props.selectedValue.includes(id)) === undefined;

    return (
        <>
            <SelectPanel
                renderAnchor={({ children, 'aria-labelledby': ariaLabelledBy, ...anchorProps }) => (
                    <Button
                        sx={{
                            mr: 1,
                            maxWidth: props.maxWidth ?? 300,
                            height: props.size === "small" ? 28 : 40,
                        }}
                        trailingAction={TriangleDownIcon}
                        aria-labelledby={` ${ariaLabelledBy}`}
                        {...anchorProps}
                    >
                        <p style={{ maxWidth: props.maxWidth, overflow: "hidden", textOverflow: "ellipsis", fontSize: props.size === "small" ? 12 : 14 }}>
                            {children || translate(`Trạng thái học sinh`)}
                        </p>
                    </Button>
                )}
                title={
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <Box sx={{ flex: 1 }}>
                            <FormControl>
                                <Checkbox
                                    checked={isSelectedAll}
                                    onChange={(e) => {
                                        if (e.target.checked) {
                                            props.onSelectionChanged(filterdData.map(x => x.id));
                                        } else {
                                            props.onSelectionChanged([]);
                                        }
                                    }}
                                />
                                <FormControl.Label>Select all</FormControl.Label>
                            </FormControl>
                        </Box>
                    </Box>
                }
                placeholderText="Search"
                open={open}
                onOpenChange={setOpen}
                items={filterdData}
                selected={_selectedDatas}
                onSelectedChange={(data: any) => {
                    props.onSelectionChanged(data.map((x: any) => x.id));
                }}
                onFilterChange={setFilter}
                showItemDividers={true}
                overlayProps={{ width: 'large', height: 'medium' }}
            />
        </>
    );
};

export default SelectBoxTrangThaiHocSinhMultiple;